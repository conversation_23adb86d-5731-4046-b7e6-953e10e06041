!function(t,e){"function"==typeof define&&define.amd?define([],e):"object"==typeof module&&module.exports?module.exports=e():t.DateFormatter=e()}("undefined"!=typeof self?self:this,function(){var t,M;return M={DAY:864e5,HOUR:3600,defaults:{dateSettings:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],meridiem:["AM","PM"],ordinal:function(t){var e=t%10,n={1:"st",2:"nd",3:"rd"};return 1!==Math.floor(t%100/10)&&n[e]?n[e]:"th"}},separators:/[ \-+\/.T:@]/g,validParts:/[dDjlNSwzWFmMntLoYyaABgGhHisueTIOPZcrU]/g,intParts:/[djwNzmnyYhHgGis]/g,tzParts:/\b(?:[PMCEA][SDP]T|(?:Pacific|Mountain|Central|Eastern|Atlantic) (?:Standard|Daylight|Prevailing) Time|(?:GMT|UTC)(?:[-+]\d{4})?)\b/g,tzClip:/[^-+\dA-Z]/g},compare:function(t,e){return"string"==typeof t&&"string"==typeof e&&t.toLowerCase()===e.toLowerCase()},lpad:function(t,e,n){var r=t.toString();return n=n||"0",r.length<e?M.lpad(n+r,e):r},merge:function(t){var e,n;for(t=t||{},e=1;e<arguments.length;e++)if(n=arguments[e])for(var r in n)n.hasOwnProperty(r)&&("object"==typeof n[r]?M.merge(t[r],n[r]):t[r]=n[r]);return t},getIndex:function(t,e){for(var n=0;n<e.length;n++)if(e[n].toLowerCase()===t.toLowerCase())return n;return-1}},(t=function(t){var e=this,n=M.merge(M.defaults,t);e.dateSettings=n.dateSettings,e.separators=n.separators,e.validParts=n.validParts,e.intParts=n.intParts,e.tzParts=n.tzParts,e.tzClip=n.tzClip}).prototype={constructor:t,getMonth:function(t){var e;return 0===(e=M.getIndex(t,this.dateSettings.monthsShort)+1)&&(e=M.getIndex(t,this.dateSettings.months)+1),e},parseDate:function(t,e){var n,r,a,u,i,s,o,c,f,l,d=this,h=!1,g=!1,p=d.dateSettings,m={date:null,year:null,month:null,day:null,hour:0,min:0,sec:0};if(!t)return null;if(t instanceof Date)return t;if("U"===e)return(a=parseInt(t))?new Date(1e3*a):t;switch(typeof t){case"number":return new Date(t);case"string":break;default:return null}if(!(n=e.match(d.validParts))||0===n.length)throw new Error("Invalid date format definition.");for(a=n.length-1;0<=a;a--)"S"===n[a]&&n.splice(a,1);for(r=t.replace(d.separators,"\0").split("\0"),a=0;a<r.length;a++)switch(u=r[a],i=parseInt(u),n[a]){case"y":case"Y":if(!i)return null;f=u.length,m.year=2===f?parseInt((i<70?"20":"19")+u):i,h=!0;break;case"m":case"n":case"M":case"F":if(isNaN(i)){if(!(0<(s=d.getMonth(u))))return null;m.month=s}else{if(!(1<=i&&i<=12))return null;m.month=i}h=!0;break;case"d":case"j":if(!(1<=i&&i<=31))return null;m.day=i,h=!0;break;case"g":case"h":if(l=r[o=-1<n.indexOf("a")?n.indexOf("a"):-1<n.indexOf("A")?n.indexOf("A"):-1],-1!==o)c=M.compare(l,p.meridiem[0])?0:M.compare(l,p.meridiem[1])?12:-1,1<=i&&i<=12&&-1!=c?m.hour=i%12==0?c:i+c:0<=i&&i<=23&&(m.hour=i);else{if(!(0<=i&&i<=23))return null;m.hour=i}g=!0;break;case"G":case"H":if(!(0<=i&&i<=23))return null;m.hour=i,g=!0;break;case"i":if(!(0<=i&&i<=59))return null;m.min=i,g=!0;break;case"s":if(!(0<=i&&i<=59))return null;m.sec=i,g=!0}if(!0===h){var y=m.year||0,D=m.month?m.month-1:0,S=m.day||1;m.date=new Date(y,D,S,m.hour,m.min,m.sec,0)}else{if(!0!==g)return null;m.date=new Date(0,0,0,m.hour,m.min,m.sec,0)}return m.date},guessDate:function(t,e){if("string"!=typeof t)return t;var n,r,a,u,i,s,o=t.replace(this.separators,"\0").split("\0"),c=e.match(this.validParts),f=new Date,l=0;if(!/^[djmn]/g.test(c[0]))return t;for(a=0;a<o.length;a++){if(l=2,i=o[a],s=parseInt(i.substr(0,2)),isNaN(s))return null;switch(a){case 0:"m"===c[0]||"n"===c[0]?f.setMonth(s-1):f.setDate(s);break;case 1:"m"===c[0]||"n"===c[0]?f.setDate(s):f.setMonth(s-1);break;case 2:if(r=f.getFullYear(),l=(n=i.length)<4?n:4,!(r=parseInt(n<4?r.toString().substr(0,4-n)+i:i.substr(0,4))))return null;f.setFullYear(r);break;case 3:f.setHours(s);break;case 4:f.setMinutes(s);break;case 5:f.setSeconds(s)}0<(u=i.substr(l)).length&&o.splice(a+1,0,u)}return f},parseFormat:function(t,r){function e(t,e){return n[t]?n[t]():e}var n,a=this,u=a.dateSettings,i=/\\?(.?)/gi;return n={d:function(){return M.lpad(n.j(),2)},D:function(){return u.daysShort[n.w()]},j:function(){return r.getDate()},l:function(){return u.days[n.w()]},N:function(){return n.w()||7},w:function(){return r.getDay()},z:function(){var t=new Date(n.Y(),n.n()-1,n.j()),e=new Date(n.Y(),0,1);return Math.round((t-e)/M.DAY)},W:function(){var t=new Date(n.Y(),n.n()-1,n.j()-n.N()+3),e=new Date(t.getFullYear(),0,4);return M.lpad(1+Math.round((t-e)/M.DAY/7),2)},F:function(){return u.months[r.getMonth()]},m:function(){return M.lpad(n.n(),2)},M:function(){return u.monthsShort[r.getMonth()]},n:function(){return r.getMonth()+1},t:function(){return new Date(n.Y(),n.n(),0).getDate()},L:function(){var t=n.Y();return t%4==0&&t%100!=0||t%400==0?1:0},o:function(){var t=n.n(),e=n.W();return n.Y()+(12===t&&e<9?1:1===t&&9<e?-1:0)},Y:function(){return r.getFullYear()},y:function(){return n.Y().toString().slice(-2)},a:function(){return n.A().toLowerCase()},A:function(){var t=n.G()<12?0:1;return u.meridiem[t]},B:function(){var t=r.getUTCHours()*M.HOUR,e=60*r.getUTCMinutes(),n=r.getUTCSeconds();return M.lpad(Math.floor((t+e+n+M.HOUR)/86.4)%1e3,3)},g:function(){return n.G()%12||12},G:function(){return r.getHours()},h:function(){return M.lpad(n.g(),2)},H:function(){return M.lpad(n.G(),2)},i:function(){return M.lpad(r.getMinutes(),2)},s:function(){return M.lpad(r.getSeconds(),2)},u:function(){return M.lpad(1e3*r.getMilliseconds(),6)},e:function(){return/\((.*)\)/.exec(String(r))[1]||"Coordinated Universal Time"},I:function(){return new Date(n.Y(),0)-Date.UTC(n.Y(),0)!=new Date(n.Y(),6)-Date.UTC(n.Y(),6)?1:0},O:function(){var t=r.getTimezoneOffset(),e=Math.abs(t);return(0<t?"-":"+")+M.lpad(100*Math.floor(e/60)+e%60,4)},P:function(){var t=n.O();return t.substr(0,3)+":"+t.substr(3,2)},T:function(){return(String(r).match(a.tzParts)||[""]).pop().replace(a.tzClip,"")||"UTC"},Z:function(){return 60*-r.getTimezoneOffset()},c:function(){return"Y-m-d\\TH:i:sP".replace(i,e)},r:function(){return"D, d M Y H:i:s O".replace(i,e)},U:function(){return r.getTime()/1e3||0}},e(t,t)},formatDate:function(t,e){var n,r,a,u,i,s="";if("string"==typeof t&&!(t=this.parseDate(t,e)))return null;if(t instanceof Date){for(a=e.length,n=0;n<a;n++)"S"!==(i=e.charAt(n))&&"\\"!==i&&(0<n&&"\\"===e.charAt(n-1)?s+=i:(u=this.parseFormat(i,t),n!==a-1&&this.intParts.test(i)&&"S"===e.charAt(n+1)&&(r=parseInt(u)||0,u+=this.dateSettings.ordinal(r)),s+=u));return s}return""}},t});