!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define("codemirror.js",t):(e=e||self).CodeMirror=t()}(this,function(){"use strict";var e=navigator.userAgent,t=navigator.platform,d=/gecko\/\d/i.test(e),n=/MSIE \d/.test(e),r=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),i=/Edge\/(\d+)/.exec(e),w=n||r||i,v=w&&(n?document.documentMode||6:+(i||r)[1]),f=!i&&/WebKit\//.test(e),r=f&&/Qt\/\d+\.\d+/.test(e),o=!i&&/Chrome\//.test(e),p=/Opera\//.test(e),c=/Apple Computer/.test(navigator.vendor),l=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),u=/PhantomJS/.test(e),s=c&&(/Mobile\/\w+/.test(e)||2<navigator.maxTouchPoints),a=/Android/.test(e),h=s||a||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),g=s||/Mac/.test(t),m=/\bCrOS\b/.test(e),t=/win/i.test(t),e=p&&e.match(/Version\/(\d*\.\d*)/);(e=e&&Number(e[1]))&&15<=e&&(f=!(p=!1));var y=g&&(r||p&&(null==e||e<12.11)),b=d||w&&9<=v;function x(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var C=function(e,t){var n=e.className,r=x(t).exec(n);r&&(t=n.slice(r.index+r[0].length),e.className=n.slice(0,r.index)+(t?r[1]+t:""))};function S(e){for(var t=e.childNodes.length;0<t;--t)e.removeChild(e.firstChild);return e}function L(e,t){return S(e).appendChild(t)}function M(e,t,n,r){var i=document.createElement(e);if(n&&(i.className=n),r&&(i.style.cssText=r),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function k(e,t,n,r){r=M(e,t,n,r);return r.setAttribute("role","presentation"),r}function T(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if((t=11==t.nodeType?t.host:t)==e)return!0}while(t=t.parentNode)}function N(){var t;try{t=document.activeElement}catch(e){t=document.body||null}for(;t&&t.shadowRoot&&t.shadowRoot.activeElement;)t=t.shadowRoot.activeElement;return t}function O(e,t){var n=e.className;x(t).test(n)||(e.className+=(n?" ":"")+t)}function A(e,t){for(var n=e.split(" "),r=0;r<n.length;r++)n[r]&&!x(n[r]).test(t)&&(t+=" "+n[r]);return t}var D=document.createRange?function(e,t,n,r){var i=document.createRange();return i.setEnd(r||e,n),i.setStart(e,t),i}:function(e,t,n){var r=document.body.createTextRange();try{r.moveToElementText(e.parentNode)}catch(e){return r}return r.collapse(!0),r.moveEnd("character",n),r.moveStart("character",t),r},W=function(e){e.select()};function H(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function F(e,t,n){for(var r in t=t||{},e)!e.hasOwnProperty(r)||!1===n&&t.hasOwnProperty(r)||(t[r]=e[r]);return t}function P(e,t,n,r,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=r||0,l=i||0;;){var s=e.indexOf("\t",o);if(s<0||t<=s)return l+(t-o);l+=s-o,l+=n-l%n,o=s+1}}s?W=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:w&&(W=function(e){try{e.select()}catch(e){}});function E(){this.id=null,this.f=null,this.time=0,this.handler=H(this.onTimeout,this)}function I(e,t){for(var n=0;n<e.length;++n)if(e[n]==t)return n;return-1}E.prototype.onTimeout=function(e){e.id=0,e.time<=+new Date?e.f():setTimeout(e.handler,e.time-+new Date)};var R=50,z={toString:function(){return"CodeMirror.Pass"}},B={scroll:!(E.prototype.set=function(e,t){this.f=t;t=+new Date+e;(!this.id||t<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,e),this.time=t)})},G={origin:"*mouse"},U={origin:"+move"};function V(e,t,n){for(var r=0,i=0;;){var o=e.indexOf("\t",r),l=(o=-1==o?e.length:o)-r;if(o==e.length||t<=i+l)return r+Math.min(l,t-i);if(i+=o-r,r=o+1,t<=(i+=n-i%n))return r}}var K=[""];function j(e){for(;K.length<=e;)K.push(X(K)+" ");return K[e]}function X(e){return e[e.length-1]}function Y(e,t){for(var n=[],r=0;r<e.length;r++)n[r]=t(e[r],r);return n}function $(){}function _(e,t){e=Object.create?Object.create(e):($.prototype=e,new $);return t&&F(t,e),e}var q=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function Z(e){return/\w/.test(e)||""<e&&(e.toUpperCase()!=e.toLowerCase()||q.test(e))}function Q(e,t){return t?!!(-1<t.source.indexOf("\\w")&&Z(e))||t.test(e):Z(e)}function J(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return;return 1}var ee=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function te(e){return 768<=e.charCodeAt(0)&&ee.test(e)}function ne(e,t,n){for(;(n<0?0<t:t<e.length)&&te(e.charAt(t));)t+=n;return t}function re(e,t,n){for(var r=n<t?-1:1;;){if(t==n)return t;var i=(t+n)/2,i=r<0?Math.ceil(i):Math.floor(i);if(i==t)return e(i)?t:n;e(i)?n=i:t=i+r}}var ie=null;function oe(e,t,n){var r;ie=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==n?r=i:ie=i),o.from==t&&(o.from!=o.to&&"before"!=n?r=i:ie=i)}return null!=r?r:ie}var le,se,ae,ue,ce,he=(le=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,se=/[stwN]/,ae=/[LRr]/,ue=/[Lb1n]/,ce=/[1n]/,function(e,t){var n="ltr"==t?"L":"R";if(0==e.length||"ltr"==t&&!le.test(e))return!1;for(var r,i=e.length,o=[],l=0;l<i;++l)o.push((r=e.charCodeAt(l))<=247?"bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN".charAt(r):1424<=r&&r<=1524?"R":1536<=r&&r<=1785?"nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111".charAt(r-1536):1774<=r&&r<=2220?"r":8192<=r&&r<=8203?"w":8204==r?"b":"L");for(var s=0,a=n;s<i;++s){var u=o[s];"m"==u?o[s]=a:a=u}for(var c=0,h=n;c<i;++c){var d=o[c];"1"==d&&"r"==h?o[c]="n":ae.test(d)&&"r"==(h=d)&&(o[c]="R")}for(var f=1,p=o[0];f<i-1;++f){var g=o[f];"+"==g&&"1"==p&&"1"==o[f+1]?o[f]="1":","!=g||p!=o[f+1]||"1"!=p&&"n"!=p||(o[f]=p),p=g}for(var m=0;m<i;++m){var v=o[m];if(","==v)o[m]="N";else if("%"==v){for(var y=void 0,y=m+1;y<i&&"%"==o[y];++y);for(var b=m&&"!"==o[m-1]||y<i&&"1"==o[y]?"1":"N",w=m;w<y;++w)o[w]=b;m=y-1}}for(var x=0,C=n;x<i;++x){var S=o[x];"L"==C&&"1"==S?o[x]="L":ae.test(S)&&(C=S)}for(var L=0;L<i;++L)if(se.test(o[L])){for(var k=void 0,k=L+1;k<i&&se.test(o[k]);++k);for(var T="L"==(L?o[L-1]:n),M=T==("L"==(k<i?o[k]:n))?T?"L":"R":n,N=L;N<k;++N)o[N]=M;L=k-1}for(var O,A=[],D=0;D<i;)if(ue.test(o[D])){var W=D;for(++D;D<i&&ue.test(o[D]);++D);A.push(new de(0,W,D))}else{var H=D,F=A.length,P="rtl"==t?1:0;for(++D;D<i&&"L"!=o[D];++D);for(var E=H;E<D;)if(ce.test(o[E])){H<E&&(A.splice(F,0,new de(1,H,E)),F+=P);var I=E;for(++E;E<D&&ce.test(o[E]);++E);A.splice(F,0,new de(2,I,E)),F+=P,H=E}else++E;H<D&&A.splice(F,0,new de(1,H,D))}return"ltr"==t&&(1==A[0].level&&(O=e.match(/^\s+/))&&(A[0].from=O[0].length,A.unshift(new de(0,0,O[0].length))),1==X(A).level&&(O=e.match(/\s+$/))&&(X(A).to-=O[0].length,A.push(new de(0,i-O[0].length,i)))),"rtl"==t?A.reverse():A});function de(e,t,n){this.level=e,this.from=t,this.to=n}function fe(e,t){var n=e.order;return n=null==n?e.order=he(e.text,t):n}var pe=[],ge=function(e,t,n){e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent?e.attachEvent("on"+t,n):(e=e._handlers||(e._handlers={}))[t]=(e[t]||pe).concat(n)};function me(e,t){return e._handlers&&e._handlers[t]||pe}function ve(e,t,n){var r;e.removeEventListener?e.removeEventListener(t,n,!1):e.detachEvent?e.detachEvent("on"+t,n):!(e=(r=e._handlers)&&r[t])||-1<(n=I(e,n))&&(r[t]=e.slice(0,n).concat(e.slice(n+1)))}function ye(e,t){var n=me(e,t);if(n.length)for(var r=Array.prototype.slice.call(arguments,2),i=0;i<n.length;++i)n[i].apply(null,r)}function be(e,t,n){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),ye(e,n||t.type,e,t),ke(t)||t.codemirrorIgnore}function we(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var n=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),r=0;r<t.length;++r)-1==I(n,t[r])&&n.push(t[r])}function xe(e,t){return 0<me(e,t).length}function Ce(e){e.prototype.on=function(e,t){ge(this,e,t)},e.prototype.off=function(e,t){ve(this,e,t)}}function Se(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function Le(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function ke(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function Te(e){Se(e),Le(e)}function Me(e){return e.target||e.srcElement}function Ne(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),t=g&&e.ctrlKey&&1==t?3:t}var Oe,Ae,De=function(){if(w&&v<9)return!1;var e=M("div");return"draggable"in e||"dragDrop"in e}();var We=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,n=[],r=e.length;t<=r;){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),l=o.indexOf("\r");-1!=l?(n.push(o.slice(0,l)),t+=l+1):(n.push(o),t=i+1)}return n}:function(e){return e.split(/\r\n?|\n/)},He=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(e){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(e){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},Fe="oncopy"in(e=M("div"))||(e.setAttribute("oncopy","return;"),"function"==typeof e.oncopy),Pe=null;var Ee={},Ie={};function Re(e){if("string"==typeof e&&Ie.hasOwnProperty(e))e=Ie[e];else if(e&&"string"==typeof e.name&&Ie.hasOwnProperty(e.name)){var t=Ie[e.name];(e=_(t="string"==typeof t?{name:t}:t,e)).name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return Re("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return Re("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function ze(e,t){t=Re(t);var n=Ee[t.name];if(!n)return ze(e,"text/plain");var r=n(e,t);if(Be.hasOwnProperty(t.name)){var i,o=Be[t.name];for(i in o)o.hasOwnProperty(i)&&(r.hasOwnProperty(i)&&(r["_"+i]=r[i]),r[i]=o[i])}if(r.name=t.name,t.helperType&&(r.helperType=t.helperType),t.modeProps)for(var l in t.modeProps)r[l]=t.modeProps[l];return r}var Be={};function Ge(e,t){F(t,Be.hasOwnProperty(e)?Be[e]:Be[e]={})}function Ue(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var n,r={};for(n in t){var i=t[n];i instanceof Array&&(i=i.concat([])),r[n]=i}return r}function Ve(e,t){for(var n;e.innerMode&&(n=e.innerMode(t))&&n.mode!=e;)t=n.state,e=n.mode;return n||{mode:e,state:t}}function Ke(e,t,n){return!e.startState||e.startState(t,n)}var je=function(e,t,n){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=n};function Xe(e,t){if((t-=e.first)<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var n=e;!n.lines;)for(var r=0;;++r){var i=n.children[r],o=i.chunkSize();if(t<o){n=i;break}t-=o}return n.lines[t]}function Ye(e,t,n){var r=[],i=t.line;return e.iter(t.line,n.line+1,function(e){e=e.text;i==n.line&&(e=e.slice(0,n.ch)),i==t.line&&(e=e.slice(t.ch)),r.push(e),++i}),r}function $e(e,t,n){var r=[];return e.iter(t,n,function(e){r.push(e.text)}),r}function _e(e,t){var n=t-e.height;if(n)for(var r=e;r;r=r.parent)r.height+=n}function qe(e){if(null==e.parent)return null;for(var t=e.parent,n=I(t.lines,e),r=t.parent;r;r=(t=r).parent)for(var i=0;r.children[i]!=t;++i)n+=r.children[i].chunkSize();return n+t.first}function Ze(e,t){var n=e.first;e:do{for(var r=0;r<e.children.length;++r){var i=e.children[r],o=i.height;if(t<o){e=i;continue e}t-=o,n+=i.chunkSize()}return n}while(!e.lines);for(var l=0;l<e.lines.length;++l){var s=e.lines[l].height;if(t<s)break;t-=s}return n+l}function Qe(e,t){return t>=e.first&&t<e.first+e.size}function Je(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function et(e,t,n){if(void 0===n&&(n=null),!(this instanceof et))return new et(e,t,n);this.line=e,this.ch=t,this.sticky=n}function tt(e,t){return e.line-t.line||e.ch-t.ch}function nt(e,t){return e.sticky==t.sticky&&0==tt(e,t)}function rt(e){return et(e.line,e.ch)}function it(e,t){return tt(e,t)<0?t:e}function ot(e,t){return tt(e,t)<0?e:t}function lt(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function st(e,t){if(t.line<e.first)return et(e.first,0);var n=e.first+e.size-1;return t.line>n?et(n,Xe(e,n).text.length):(e=Xe(e,(n=t).line).text.length,null==(t=n.ch)||e<t?et(n.line,e):t<0?et(n.line,0):n)}function at(e,t){for(var n=[],r=0;r<t.length;r++)n[r]=st(e,t[r]);return n}je.prototype.eol=function(){return this.pos>=this.string.length},je.prototype.sol=function(){return this.pos==this.lineStart},je.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},je.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},je.prototype.eat=function(e){var t=this.string.charAt(this.pos),e="string"==typeof e?t==e:t&&(e.test?e.test(t):e(t));if(e)return++this.pos,t},je.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},je.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},je.prototype.skipToEnd=function(){this.pos=this.string.length},je.prototype.skipTo=function(e){e=this.string.indexOf(e,this.pos);if(-1<e)return this.pos=e,!0},je.prototype.backUp=function(e){this.pos-=e},je.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=P(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?P(this.string,this.lineStart,this.tabSize):0)},je.prototype.indentation=function(){return P(this.string,null,this.tabSize)-(this.lineStart?P(this.string,this.lineStart,this.tabSize):0)},je.prototype.match=function(e,t,n){if("string"!=typeof e){var r=this.string.slice(this.pos).match(e);return r&&0<r.index?null:(r&&!1!==t&&(this.pos+=r[0].length),r)}function i(e){return n?e.toLowerCase():e}if(i(this.string.substr(this.pos,e.length))==i(e))return!1!==t&&(this.pos+=e.length),!0},je.prototype.current=function(){return this.string.slice(this.start,this.pos)},je.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},je.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},je.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};function ut(e,t){this.state=e,this.lookAhead=t}var ct=function(e,t,n,r){this.state=t,this.doc=e,this.line=n,this.maxLookAhead=r||0,this.baseTokens=null,this.baseTokenPos=1};function ht(t,n,r,e){var a=[t.state.modeGen],i={};wt(t,n.text,t.doc.mode,r,function(e,t){return a.push(e,t)},i,e);for(var u=r.state,o=0;o<t.state.overlays.length;++o)!function(e){r.baseTokens=a;var o=t.state.overlays[e],l=1,s=0;r.state=!0,wt(t,n.text,o.mode,r,function(e,t){for(var n=l;s<e;){var r=a[l];e<r&&a.splice(l,1,e,a[l+1],r),l+=2,s=Math.min(e,r)}if(t)if(o.opaque)a.splice(n,l-n,e,"overlay "+t),l=n+2;else for(;n<l;n+=2){var i=a[n+1];a[n+1]=(i?i+" ":"")+"overlay "+t}},i),r.state=u,r.baseTokens=null,r.baseTokenPos=1}(o);return{styles:a,classes:i.bgClass||i.textClass?i:null}}function dt(e,t,n){var r,i,o;return t.styles&&t.styles[0]==e.state.modeGen||(r=ft(e,qe(t)),i=t.text.length>e.options.maxHighlightLength&&Ue(e.doc.mode,r.state),o=ht(e,t,r),i&&(r.state=i),t.stateAfter=r.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),n===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))),t.styles}function ft(n,r,e){var t=n.doc,i=n.display;if(!t.mode.startState)return new ct(t,!0,r);var o=function(e,t,n){for(var r,i,o=e.doc,l=n?-1:t-(e.doc.mode.innerMode?1e3:100),s=t;l<s;--s){if(s<=o.first)return o.first;var a=Xe(o,s-1),u=a.stateAfter;if(u&&(!n||s+(u instanceof ut?u.lookAhead:0)<=o.modeFrontier))return s;a=P(a.text,null,e.options.tabSize);(null==i||a<r)&&(i=s-1,r=a)}return i}(n,r,e),l=o>t.first&&Xe(t,o-1).stateAfter,s=l?ct.fromSaved(t,l,o):new ct(t,Ke(t.mode),o);return t.iter(o,r,function(e){pt(n,e.text,s);var t=s.line;e.stateAfter=t==r-1||t%5==0||t>=i.viewFrom&&t<i.viewTo?s.save():null,s.nextLine()}),e&&(t.modeFrontier=s.line),s}function pt(e,t,n,r){var i=e.doc.mode,o=new je(t,e.options.tabSize,n);for(o.start=o.pos=r||0,""==t&&gt(i,n.state);!o.eol();)mt(i,o,n.state),o.start=o.pos}function gt(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){t=Ve(e,t);return t.mode.blankLine?t.mode.blankLine(t.state):void 0}}function mt(e,t,n,r){for(var i=0;i<10;i++){r&&(r[0]=Ve(e,n).mode);var o=e.token(t,n);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}ct.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},ct.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},ct.prototype.nextLine=function(){this.line++,0<this.maxLookAhead&&this.maxLookAhead--},ct.fromSaved=function(e,t,n){return t instanceof ut?new ct(e,Ue(e.mode,t.state),n,t.lookAhead):new ct(e,Ue(e.mode,t),n)},ct.prototype.save=function(e){e=!1!==e?Ue(this.doc.mode,this.state):this.state;return 0<this.maxLookAhead?new ut(e,this.maxLookAhead):e};var vt=function(e,t,n){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=n};function yt(e,t,n,r){var i,o,l=e.doc,s=l.mode,a=Xe(l,(t=st(l,t)).line),u=ft(e,t.line,n),c=new je(a.text,e.options.tabSize,u);for(r&&(o=[]);(r||c.pos<t.ch)&&!c.eol();)c.start=c.pos,i=mt(s,c,u.state),r&&o.push(new vt(c,i,Ue(l.mode,u.state)));return r?o:new vt(c,i,u.state)}function bt(e,t){if(e)for(;;){var n=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!n)break;e=e.slice(0,n.index)+e.slice(n.index+n[0].length);var r=n[1]?"bgClass":"textClass";null==t[r]?t[r]=n[2]:new RegExp("(?:^|\\s)"+n[2]+"(?:$|\\s)").test(t[r])||(t[r]+=" "+n[2])}return e}function wt(e,t,n,r,i,o,l){var s=n.flattenSpans;null==s&&(s=e.options.flattenSpans);var a=0,u=null,c=new je(t,e.options.tabSize,r),h=e.options.addModeClass&&[null];for(""==t&&bt(gt(n,r.state),o);!c.eol();){var d,f=c.pos>e.options.maxHighlightLength?(s=!1,l&&pt(e,t,r,c.pos),c.pos=t.length,null):bt(mt(n,c,r.state,h),o);if(!h||(d=h[0].name)&&(f="m-"+(f?d+" "+f:d)),!s||u!=f){for(;a<c.start;)i(a=Math.min(c.start,a+5e3),u);u=f}c.start=c.pos}for(;a<c.pos;){var p=Math.min(c.pos,a+5e3);i(p,u),a=p}}var xt=!1,Ct=!1;function St(e,t,n){this.marker=e,this.from=t,this.to=n}function Lt(e,t){if(e)for(var n=0;n<e.length;++n){var r=e[n];if(r.marker==t)return r}}function kt(e,t){if(t.full)return null;var n=Qe(e,t.from.line)&&Xe(e,t.from.line).markedSpans,r=Qe(e,t.to.line)&&Xe(e,t.to.line).markedSpans;if(!n&&!r)return null;var i=t.from.ch,o=t.to.ch,e=0==tt(t.from,t.to),l=function(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o,l=e[i],s=l.marker;!(null==l.from||(s.inclusiveLeft?l.from<=t:l.from<t))&&(l.from!=t||"bookmark"!=s.type||n&&l.marker.insertLeft)||(o=null==l.to||(s.inclusiveRight?l.to>=t:l.to>t),(r=r||[]).push(new St(s,l.from,o?null:l.to)))}return r}(n,i,e),s=function(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o,l=e[i],s=l.marker;!(null==l.to||(s.inclusiveRight?l.to>=t:l.to>t))&&(l.from!=t||"bookmark"!=s.type||n&&!l.marker.insertLeft)||(o=null==l.from||(s.inclusiveLeft?l.from<=t:l.from<t),(r=r||[]).push(new St(s,o?null:l.from-t,null==l.to?null:l.to-t)))}return r}(r,o,e),a=1==t.text.length,u=X(t.text).length+(a?i:0);if(l)for(var c=0;c<l.length;++c){var h,d=l[c];null==d.to&&((h=Lt(s,d.marker))?a&&(d.to=null==h.to?null:h.to+u):d.to=i)}if(s)for(var f=0;f<s.length;++f){var p=s[f];null!=p.to&&(p.to+=u),null==p.from?Lt(l,p.marker)||(p.from=u,a&&(l=l||[]).push(p)):(p.from+=u,a&&(l=l||[]).push(p))}l=l&&Tt(l),s&&s!=l&&(s=Tt(s));var g=[l];if(!a){var m,v=t.text.length-2;if(0<v&&l)for(var y=0;y<l.length;++y)null==l[y].to&&(m=m||[]).push(new St(l[y].marker,null,null));for(var b=0;b<v;++b)g.push(m);g.push(s)}return g}function Tt(e){for(var t=0;t<e.length;++t){var n=e[t];null!=n.from&&n.from==n.to&&!1!==n.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function Mt(e){var t=e.markedSpans;if(t){for(var n=0;n<t.length;++n)t[n].marker.detachLine(e);e.markedSpans=null}}function Nt(e,t){if(t){for(var n=0;n<t.length;++n)t[n].marker.attachLine(e);e.markedSpans=t}}function Ot(e){return e.inclusiveLeft?-1:0}function At(e){return e.inclusiveRight?1:0}function Dt(e,t){var n=e.lines.length-t.lines.length;if(0!=n)return n;var r=e.find(),i=t.find(),n=tt(r.from,i.from)||Ot(e)-Ot(t);if(n)return-n;i=tt(r.to,i.to)||At(e)-At(t);return i||t.id-e.id}function Wt(e,t){var n,r=Ct&&e.markedSpans;if(r)for(var i,o=0;o<r.length;++o)(i=r[o]).marker.collapsed&&null==(t?i.from:i.to)&&(!n||Dt(n,i.marker)<0)&&(n=i.marker);return n}function Ht(e){return Wt(e,!0)}function Ft(e){return Wt(e,!1)}function Pt(e,t,n,r,i){var t=Xe(e,t),o=Ct&&t.markedSpans;if(o)for(var l=0;l<o.length;++l){var s=o[l];if(s.marker.collapsed){var a=s.marker.find(0),u=tt(a.from,n)||Ot(s.marker)-Ot(i),c=tt(a.to,r)||At(s.marker)-At(i);if(!(0<=u&&c<=0||u<=0&&0<=c)&&(u<=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?0<=tt(a.to,n):0<tt(a.to,n))||0<=u&&(s.marker.inclusiveRight&&i.inclusiveLeft?tt(a.from,r)<=0:tt(a.from,r)<0)))return 1}}}function Et(e){for(var t;t=Ht(e);)e=t.find(-1,!0).line;return e}function It(e,t){var n=Xe(e,t),e=Et(n);return n==e?t:qe(e)}function Rt(e,t){if(t>e.lastLine())return t;var n,r=Xe(e,t);if(!zt(e,r))return t;for(;n=Ft(r);)r=n.find(1,!0).line;return qe(r)+1}function zt(e,t){var n=Ct&&t.markedSpans;if(n)for(var r,i=0;i<n.length;++i)if((r=n[i]).marker.collapsed){if(null==r.from)return!0;if(!r.marker.widgetNode&&0==r.from&&r.marker.inclusiveLeft&&function e(t,n,r){if(null==r.to){var i=r.marker.find(1,!0);return e(t,i.line,Lt(i.line.markedSpans,r.marker))}if(r.marker.inclusiveRight&&r.to==n.text.length)return!0;for(var o=void 0,l=0;l<n.markedSpans.length;++l)if((o=n.markedSpans[l]).marker.collapsed&&!o.marker.widgetNode&&o.from==r.to&&(null==o.to||o.to!=r.from)&&(o.marker.inclusiveLeft||r.marker.inclusiveRight)&&e(t,n,o))return!0}(e,t,r))return!0}}function Bt(e){for(var t=0,n=(e=Et(e)).parent,r=0;r<n.lines.length;++r){var i=n.lines[r];if(i==e)break;t+=i.height}for(var o=n.parent;o;o=(n=o).parent)for(var l=0;l<o.children.length;++l){var s=o.children[l];if(s==n)break;t+=s.height}return t}function Gt(e){if(0==e.height)return 0;for(var t,n=e.text.length,r=e;t=Ht(r);){var i=t.find(0,!0),r=i.from.line;n+=i.from.ch-i.to.ch}for(r=e;t=Ft(r);){var o=t.find(0,!0);n-=r.text.length-o.from.ch,n+=(r=o.to.line).text.length-o.to.ch}return n}function Ut(e){var n=e.display,e=e.doc;n.maxLine=Xe(e,e.first),n.maxLineLength=Gt(n.maxLine),n.maxLineChanged=!0,e.iter(function(e){var t=Gt(e);t>n.maxLineLength&&(n.maxLineLength=t,n.maxLine=e)})}var Vt=function(e,t,n){this.text=e,Nt(this,t),this.height=n?n(this):1};Vt.prototype.lineNo=function(){return qe(this)},Ce(Vt);var Kt={},jt={};function Xt(e,t){if(!e||/^\s*$/.test(e))return null;t=t.addModeClass?jt:Kt;return t[e]||(t[e]=e.replace(/\S+/g,"cm-$&"))}function Yt(e,t){var n=k("span",null,null,f?"padding-right: .1px":null),r={pre:k("pre",[n],"CodeMirror-line"),content:n,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var o=i?t.rest[i-1]:t.line,l=void 0;r.pos=0,r.addToken=_t,function(e){if(null!=Ae)return Ae;var t=L(e,document.createTextNode("AخA")),n=D(t,0,1).getBoundingClientRect(),t=D(t,1,2).getBoundingClientRect();return S(e),n&&n.left!=n.right&&(Ae=t.right-n.right<3)}(e.display.measure)&&(l=fe(o,e.doc.direction))&&(r.addToken=function(h,d){return function(e,t,n,r,i,o,l){n=n?n+" cm-force-border":"cm-force-border";for(var s=e.pos,a=s+t.length;;){for(var u=void 0,c=0;c<d.length&&!((u=d[c]).to>s&&u.from<=s);c++);if(u.to>=a)return h(e,t,n,r,i,o,l);h(e,t.slice(0,u.to-s),n,r,null,o,l),r=null,t=t.slice(u.to-s),s=u.to}}}(r.addToken,l)),r.map=[],function(e,t,n){var r=e.markedSpans,i=e.text,o=0;if(r)for(var l,s,a,u,c,h,d,f=i.length,p=0,g=1,m="",v=0;;){if(v==p){a=u=c=s="",h=d=null,v=1/0;for(var y=[],b=void 0,w=0;w<r.length;++w){var x=r[w],C=x.marker;if("bookmark"==C.type&&x.from==p&&C.widgetNode)y.push(C);else if(x.from<=p&&(null==x.to||x.to>p||C.collapsed&&x.to==p&&x.from==p)){if(null!=x.to&&x.to!=p&&v>x.to&&(v=x.to,u=""),C.className&&(a+=" "+C.className),C.css&&(s=(s?s+";":"")+C.css),C.startStyle&&x.from==p&&(c+=" "+C.startStyle),C.endStyle&&x.to==v&&(b=b||[]).push(C.endStyle,x.to),C.title&&((d=d||{}).title=C.title),C.attributes)for(var S in C.attributes)(d=d||{})[S]=C.attributes[S];C.collapsed&&(!h||Dt(h.marker,C)<0)&&(h=x)}else x.from>p&&v>x.from&&(v=x.from)}if(b)for(var L=0;L<b.length;L+=2)b[L+1]==v&&(u+=" "+b[L]);if(!h||h.from==p)for(var k=0;k<y.length;++k)qt(t,0,y[k]);if(h&&(h.from||0)==p){if(qt(t,(null==h.to?f+1:h.to)-p,h.marker,null==h.from),null==h.to)return;h.to==p&&(h=!1)}}if(f<=p)break;for(var T=Math.min(f,v);;){if(m){var M,N=p+m.length;if(h||(M=T<N?m.slice(0,T-p):m,t.addToken(t,M,l?l+a:a,c,p+M.length==v?u:"",s,d)),T<=N){m=m.slice(T-p),p=T;break}p=N,c=""}m=i.slice(o,o=n[g++]),l=Xt(n[g++],t.cm.options)}}else for(var O=1;O<n.length;O+=2)t.addToken(t,i.slice(o,o=n[O]),Xt(n[O+1],t.cm.options))}(o,r,dt(e,o,t!=e.display.externalMeasured&&qe(o))),o.styleClasses&&(o.styleClasses.bgClass&&(r.bgClass=A(o.styleClasses.bgClass,r.bgClass||"")),o.styleClasses.textClass&&(r.textClass=A(o.styleClasses.textClass,r.textClass||""))),0==r.map.length&&r.map.push(0,0,r.content.appendChild(function(e){null==Oe&&(t=M("span","​"),L(e,M("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(Oe=t.offsetWidth<=1&&2<t.offsetHeight&&!(w&&v<8)));var t=Oe?M("span","​"):M("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return t.setAttribute("cm-text",""),t}(e.display.measure))),0==i?(t.measure.map=r.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(r.map),(t.measure.caches||(t.measure.caches=[])).push({}))}return f&&(n=r.content.lastChild,(/\bcm-tab\b/.test(n.className)||n.querySelector&&n.querySelector(".cm-tab"))&&(r.content.className="cm-tab-wrap-hack")),ye(e,"renderLine",e,t.line,r.pre),r.pre.className&&(r.textClass=A(r.pre.className,r.textClass||"")),r}function $t(e){var t=M("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function _t(e,t,n,r,i,o,l){if(t){var s=e.splitSpaces?function(e,t){if(1<e.length&&!/  /.test(e))return e;for(var n=t,r="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!n||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),r+=o,n=" "==o}return r}(t,e.trailingSpace):t,a=e.cm.state.specialChars,u=!1;if(a.test(t))for(var c=document.createDocumentFragment(),h=0;;){a.lastIndex=h;var d=a.exec(t),f=d?d.index-h:t.length-h;if(f&&(p=document.createTextNode(s.slice(h,h+f)),w&&v<9?c.appendChild(M("span",[p])):c.appendChild(p),e.map.push(e.pos,e.pos+f,p),e.col+=f,e.pos+=f),!d)break;h+=1+f;var p=void 0;"\t"==d[0]?(f=(f=e.cm.options.tabSize)-e.col%f,(p=c.appendChild(M("span",j(f),"cm-tab"))).setAttribute("role","presentation"),p.setAttribute("cm-text","\t"),e.col+=f):("\r"==d[0]||"\n"==d[0]?(p=c.appendChild(M("span","\r"==d[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",d[0]):((p=e.cm.options.specialCharPlaceholder(d[0])).setAttribute("cm-text",d[0]),w&&v<9?c.appendChild(M("span",[p])):c.appendChild(p)),e.col+=1),e.map.push(e.pos,e.pos+1,p),e.pos++}else e.col+=t.length,c=document.createTextNode(s),e.map.push(e.pos,e.pos+t.length,c),w&&v<9&&(u=!0),e.pos+=t.length;if(e.trailingSpace=32==s.charCodeAt(t.length-1),n||r||i||u||o||l){n=n||"";r&&(n+=r),i&&(n+=i);var g=M("span",[c],n,o);if(l)for(var m in l)l.hasOwnProperty(m)&&"style"!=m&&"class"!=m&&g.setAttribute(m,l[m]);return e.content.appendChild(g)}e.content.appendChild(c)}}function qt(e,t,n,r){var i=!r&&n.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!r&&e.cm.display.input.needsContentAttribute&&(i=i||e.content.appendChild(document.createElement("span"))).setAttribute("cm-marker",n.id),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function Zt(e,t,n){this.line=t,this.rest=function(e){for(var t,n;t=Ft(e);)e=t.find(1,!0).line,(n=n||[]).push(e);return n}(t),this.size=this.rest?qe(X(this.rest))-n+1:1,this.node=this.text=null,this.hidden=zt(e,t)}function Qt(e,t,n){for(var r=[],i=t;i<n;i=l){var o=new Zt(e.doc,Xe(e.doc,i),i),l=i+o.size;r.push(o)}return r}var Jt=null;function en(e,t){e=e.ownsGroup;if(e)try{!function(e){var t=e.delayedCallbacks,n=0;do{for(;n<t.length;n++)t[n].call(null);for(var r=0;r<e.ops.length;r++){var i=e.ops[r];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(n<t.length)}(e)}finally{Jt=null,t(e)}}var tn=null;function nn(e,t){var n=me(e,t);if(n.length){var r,i=Array.prototype.slice.call(arguments,2);Jt?r=Jt.delayedCallbacks:tn?r=tn:(r=tn=[],setTimeout(rn,0));for(var o=0;o<n.length;++o)!function(e){r.push(function(){return n[e].apply(null,i)})}(o)}}function rn(){var e=tn;tn=null;for(var t=0;t<e.length;++t)e[t]()}function on(e,t,n,r){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?function(e,t){var n=t.text.className,r=sn(e,t);t.text==t.node&&(t.node=r.pre);t.text.parentNode.replaceChild(r.pre,t.text),t.text=r.pre,r.bgClass!=t.bgClass||r.textClass!=t.textClass?(t.bgClass=r.bgClass,t.textClass=r.textClass,an(e,t)):n&&(t.text.className=n)}(e,t):"gutter"==o?un(e,t,n,r):"class"==o?an(e,t):"widget"==o&&function(e,t,n){t.alignable&&(t.alignable=null);for(var r=x("CodeMirror-linewidget"),i=t.node.firstChild,o=void 0;i;i=o)o=i.nextSibling,r.test(i.className)&&t.node.removeChild(i);cn(e,t,n)}(e,t,r)}t.changes=null}function ln(e){return e.node==e.text&&(e.node=M("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),w&&v<8&&(e.node.style.zIndex=2)),e.node}function sn(e,t){var n=e.display.externalMeasured;return n&&n.line==t.line?(e.display.externalMeasured=null,t.measure=n.measure,n.built):Yt(e,t)}function an(e,t){var n,r;n=e,(r=(i=t).bgClass?i.bgClass+" "+(i.line.bgClass||""):i.line.bgClass)&&(r+=" CodeMirror-linebackground"),i.background?r?i.background.className=r:(i.background.parentNode.removeChild(i.background),i.background=null):r&&(e=ln(i),i.background=e.insertBefore(M("div",null,r),e.firstChild),n.display.input.setUneditable(i.background)),t.line.wrapClass?ln(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var i=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=i||""}function un(e,t,n,r){t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass&&(o=ln(t),t.gutterBackground=M("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px; width: "+r.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),o.insertBefore(t.gutterBackground,t.text));var i=t.line.gutterMarkers;if(e.options.lineNumbers||i){var o=ln(t),l=t.gutter=M("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px");if(l.setAttribute("aria-hidden","true"),e.display.input.setUneditable(l),o.insertBefore(l,t.text),t.line.gutterClass&&(l.className+=" "+t.line.gutterClass),!e.options.lineNumbers||i&&i["CodeMirror-linenumbers"]||(t.lineNumber=l.appendChild(M("div",Je(e.options,n),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+r.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),i)for(var s=0;s<e.display.gutterSpecs.length;++s){var a=e.display.gutterSpecs[s].className,u=i.hasOwnProperty(a)&&i[a];u&&l.appendChild(M("div",[u],"CodeMirror-gutter-elt","left: "+r.gutterLeft[a]+"px; width: "+r.gutterWidth[a]+"px"))}}}function cn(e,t,n){if(hn(e,t.line,t,n,!0),t.rest)for(var r=0;r<t.rest.length;r++)hn(e,t.rest[r],t,n,!1)}function hn(e,t,n,r,i){if(t.widgets)for(var o=ln(n),l=0,s=t.widgets;l<s.length;++l){var a=s[l],u=M("div",[a.node],"CodeMirror-linewidget"+(a.className?" "+a.className:""));a.handleMouseEvents||u.setAttribute("cm-ignore-events","true"),function(e,t,n,r){e.noHScroll&&((n.alignable||(n.alignable=[])).push(t),n=r.wrapperWidth,t.style.left=r.fixedPos+"px",e.coverGutter||(n-=r.gutterTotalWidth,t.style.paddingLeft=r.gutterTotalWidth+"px"),t.style.width=n+"px");e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-r.gutterTotalWidth+"px"))}(a,u,n,r),e.display.input.setUneditable(u),i&&a.above?o.insertBefore(u,n.gutter||n.text):o.appendChild(u),nn(a,"redraw")}}function dn(e){if(null!=e.height)return e.height;var t,n=e.doc.cm;return n?(T(document.body,e.node)||(t="position: relative;",e.coverGutter&&(t+="margin-left: -"+n.display.gutters.offsetWidth+"px;"),e.noHScroll&&(t+="width: "+n.display.wrapper.clientWidth+"px;"),L(n.display.measure,M("div",[e.node],null,t))),e.height=e.node.parentNode.offsetHeight):0}function fn(e,t){for(var n=Me(t);n!=e.wrapper;n=n.parentNode)if(!n||1==n.nodeType&&"true"==n.getAttribute("cm-ignore-events")||n.parentNode==e.sizer&&n!=e.mover)return 1}function pn(e){return e.lineSpace.offsetTop}function gn(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function mn(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=L(e.measure,M("pre","x","CodeMirror-line-like")),t=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,t={left:parseInt(t.paddingLeft),right:parseInt(t.paddingRight)};return isNaN(t.left)||isNaN(t.right)||(e.cachedPaddingH=t),t}function vn(e){return R-e.display.nativeBarWidth}function yn(e){return e.display.scroller.clientWidth-vn(e)-e.display.barWidth}function bn(e){return e.display.scroller.clientHeight-vn(e)-e.display.barHeight}function wn(e,t,n){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};if(e.rest){for(var r=0;r<e.rest.length;r++)if(e.rest[r]==t)return{map:e.measure.maps[r],cache:e.measure.caches[r]};for(var i=0;i<e.rest.length;i++)if(qe(e.rest[i])>n)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}}function xn(e,t,n,r){return Ln(e,Sn(e,t),n,r)}function Cn(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[Jn(e,t)];e=e.display.externalMeasured;return e&&t>=e.lineN&&t<e.lineN+e.size?e:void 0}function Sn(e,t){var n,r,i=qe(t),o=Cn(e,i);o&&!o.text?o=null:o&&o.changes&&(on(e,o,i,$n(e)),e.curOp.forceUpdate=!0),o||(n=e,e=qe(r=Et(r=t)),(r=n.display.externalMeasured=new Zt(n.doc,r,e)).lineN=e,e=r.built=Yt(n,r),r.text=e.pre,L(n.display.lineMeasure,e.pre),o=r);i=wn(o,t,i);return{line:t,view:o,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function Ln(e,t,n,r,i){var o,l=(n=t.before?-1:n)+(r||"");return t.cache.hasOwnProperty(l)?o=t.cache[l]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(function(e,t,n){var r=e.options.lineWrapping,e=r&&yn(e);if(!t.measure.heights||r&&t.measure.width!=e){var i=t.measure.heights=[];if(r){t.measure.width=e;for(var o=t.text.firstChild.getClientRects(),l=0;l<o.length-1;l++){var s=o[l],a=o[l+1];2<Math.abs(s.bottom-a.bottom)&&i.push((s.bottom+a.top)/2-n.top)}}i.push(n.bottom-n.top)}}(e,t.view,t.rect),t.hasHeights=!0),(o=function(e,t,n,r){var i,o=Mn(t.map,n,r),l=o.node,s=o.start,a=o.end,u=o.collapse;if(3==l.nodeType){for(var c=0;c<4;c++){for(;s&&te(t.line.text.charAt(o.coverStart+s));)--s;for(;o.coverStart+a<o.coverEnd&&te(t.line.text.charAt(o.coverStart+a));)++a;if((i=w&&v<9&&0==s&&a==o.coverEnd-o.coverStart?l.parentNode.getBoundingClientRect():function(e,t){var n=Tn;if("left"==t)for(var r=0;r<e.length&&(n=e[r]).left==n.right;r++);else for(var i=e.length-1;0<=i&&(n=e[i]).left==n.right;i--);return n}(D(l,s,a).getClientRects(),r)).left||i.right||0==s)break;a=s,s-=1,u="right"}w&&v<11&&(i=function(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!function(e){if(null!=Pe)return Pe;var e=(t=L(e,M("span","x"))).getBoundingClientRect(),t=D(t,0,1).getBoundingClientRect();return Pe=1<Math.abs(e.left-t.left)}(e))return t;var n=screen.logicalXDPI/screen.deviceXDPI,e=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*n,right:t.right*n,top:t.top*e,bottom:t.bottom*e}}(e.display.measure,i))}else 0<s&&(u=r="right"),i=e.options.lineWrapping&&1<(g=l.getClientRects()).length?g["right"==r?g.length-1:0]:l.getBoundingClientRect();!(w&&v<9)||s||i&&(i.left||i.right)||(m=l.parentNode.getClientRects()[0],i=m?{left:m.left,right:m.left+Yn(e.display),top:m.top,bottom:m.bottom}:Tn);for(var h=i.top-t.rect.top,n=i.bottom-t.rect.top,d=(h+n)/2,f=t.view.measure.heights,p=0;p<f.length-1&&!(d<f[p]);p++);var g=p?f[p-1]:0,m=f[p],m={left:("right"==u?i.right:i.left)-t.rect.left,right:("left"==u?i.left:i.right)-t.rect.left,top:g,bottom:m};i.left||i.right||(m.bogus=!0);e.options.singleCursorHeightPerLine||(m.rtop=h,m.rbottom=n);return m}(e,t,n,r)).bogus||(t.cache[l]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var kn,Tn={left:0,right:0,top:0,bottom:0};function Mn(e,t,n){for(var r,i,o,l,s,a,u=0;u<e.length;u+=3)if(s=e[u],a=e[u+1],t<s?(i=0,o=1,l="left"):t<a?o=(i=t-s)+1:(u==e.length-3||t==a&&e[u+3]>t)&&(i=(o=a-s)-1,a<=t&&(l="right")),null!=i){if(r=e[u+2],s==a&&n==(r.insertLeft?"left":"right")&&(l=n),"left"==n&&0==i)for(;u&&e[u-2]==e[u-3]&&e[u-1].insertLeft;)r=e[2+(u-=3)],l="left";if("right"==n&&i==a-s)for(;u<e.length-3&&e[u+3]==e[u+4]&&!e[u+5].insertLeft;)r=e[(u+=3)+2],l="right";break}return{node:r,start:i,end:o,collapse:l,coverStart:s,coverEnd:a}}function Nn(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function On(e){e.display.externalMeasure=null,S(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)Nn(e.display.view[t])}function An(e){On(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function Dn(){return o&&a?-(document.body.getBoundingClientRect().left-parseInt(getComputedStyle(document.body).marginLeft)):window.pageXOffset||(document.documentElement||document.body).scrollLeft}function Wn(){return o&&a?-(document.body.getBoundingClientRect().top-parseInt(getComputedStyle(document.body).marginTop)):window.pageYOffset||(document.documentElement||document.body).scrollTop}function Hn(e){var t=Et(e).widgets,n=0;if(t)for(var r=0;r<t.length;++r)t[r].above&&(n+=dn(t[r]));return n}function Fn(e,t,n,r,i){if(i||(i=Hn(t),n.top+=i,n.bottom+=i),"line"==r)return n;r=r||"local";t=Bt(t);return"local"==r?t+=pn(e.display):t-=e.display.viewOffset,"page"!=r&&"window"!=r||(t+=(e=e.display.lineSpace.getBoundingClientRect()).top+("window"==r?0:Wn()),r=e.left+("window"==r?0:Dn()),n.left+=r,n.right+=r),n.top+=t,n.bottom+=t,n}function Pn(e,t,n){if("div"==n)return t;var r=t.left,t=t.top;"page"==n?(r-=Dn(),t-=Wn()):"local"!=n&&n||(r+=(n=e.display.sizer.getBoundingClientRect()).left,t+=n.top);e=e.display.lineSpace.getBoundingClientRect();return{left:r-e.left,top:t-e.top}}function En(e,t,n,r,i){return Fn(e,r=r||Xe(e.doc,t.line),xn(e,r,t.ch,i),n)}function In(n,e,r,i,o,l){function s(e,t){e=Ln(n,o,e,t?"right":"left",l);return t?e.left=e.right:e.right=e.left,Fn(n,i,e,r)}i=i||Xe(n.doc,e.line),o=o||Sn(n,i);var a=fe(i,n.doc.direction),t=e.ch,u=e.sticky;if(t>=i.text.length?(t=i.text.length,u="before"):t<=0&&(t=0,u="after"),!a)return s("before"==u?t-1:t,"before"==u);function c(e,t,n){return s(n?e-1:e,1==a[t].level!=n)}var h=oe(a,t,u),e=ie,h=c(t,h,"before"==u);return null!=e&&(h.other=c(t,e,"before"!=u)),h}function Rn(e,t){var n=0;t=st(e.doc,t),e.options.lineWrapping||(n=Yn(e.display)*t.ch);t=Xe(e.doc,t.line),e=Bt(t)+pn(e.display);return{left:n,right:n,top:e,bottom:e+t.height}}function zn(e,t,n,r,i){n=et(e,t,n);return n.xRel=i,r&&(n.outside=r),n}function Bn(e,t,n){var r=e.doc;if((n+=e.display.viewOffset)<0)return zn(r.first,0,null,-1,-1);var i=Ze(r,n),o=r.first+r.size-1;if(o<i)return zn(r.first+r.size-1,Xe(r,o).text.length,null,1,1);t<0&&(t=0);for(var l=Xe(r,i);;){var s=function(n,e,t,r,i){i-=Bt(e);var o=Sn(n,e),l=Hn(e),s=0,a=e.text.length,u=!0,c=fe(e,n.doc.direction);c&&(f=(n.options.lineWrapping?jn:Kn)(n,e,t,o,c,r,i),u=1!=f.level,s=u?f.from:f.to-1,a=u?f.to:f.from-1);var h=null,d=null,c=re(function(e){var t=Ln(n,o,e);return t.top+=l,t.bottom+=l,Vn(t,r,i,!1)&&(t.top<=i&&t.left<=r&&(h=e,d=t),1)},s,a),f=!1;{var p,g;d?(p=r-d.left<d.right-r,c=h+((g=p==u)?0:1),g=g?"after":"before",p=p?d.left:d.right):(u||c!=a&&c!=s||c++,g=0==c||c!=e.text.length&&Ln(n,o,c-(u?1:0)).bottom+l<=i==u?"after":"before",u=In(n,et(t,c,g),"line",e,o),p=u.left,f=i<u.top?-1:i>=u.bottom?1:0)}return c=ne(e.text,c,1),zn(t,c,g,f,r-p)}(e,l,i,t,n),a=function(e,t){var n,r=Ct&&e.markedSpans;if(r)for(var i=0;i<r.length;++i){var o=r[i];o.marker.collapsed&&(null==o.from||o.from<t)&&(null==o.to||o.to>t)&&(!n||Dt(n,o.marker)<0)&&(n=o.marker)}return n}(l,s.ch+(0<s.xRel||0<s.outside?1:0));if(!a)return s;a=a.find(1);if(a.line==i)return a;l=Xe(r,i=a.line)}}function Gn(t,e,n,r){r-=Hn(e);var i=e.text.length,e=re(function(e){return Ln(t,n,e-1).bottom<=r},i,0);return{begin:e,end:i=re(function(e){return Ln(t,n,e).top>r},e,i)}}function Un(e,t,n,r){return Gn(e,t,n=n||Sn(e,t),Fn(e,t,Ln(e,n,r),"line").top)}function Vn(e,t,n,r){return!(e.bottom<=n)&&(e.top>n||(r?e.left:e.right)>t)}function Kn(n,r,i,o,l,s,a){var e,t=re(function(e){var t=l[e],e=1!=t.level;return Vn(In(n,et(i,e?t.to:t.from,e?"before":"after"),"line",r,o),s,a,!0)},0,l.length-1),u=l[t];return 0<t&&(e=1!=u.level,Vn(e=In(n,et(i,e?u.from:u.to,e?"after":"before"),"line",r,o),s,a,!0)&&e.top>a&&(u=l[t-1])),u}function jn(e,t,n,r,i,o,l){var l=Gn(e,t,r,l),s=l.begin,a=l.end;/\s/.test(t.text.charAt(a-1))&&a--;for(var u=null,c=null,h=0;h<i.length;h++){var d,f=i[h];f.from>=a||f.to<=s||(d=(d=Ln(e,r,1!=f.level?Math.min(a,f.to)-1:Math.max(s,f.from)).right)<o?o-d+1e9:d-o,(!u||d<c)&&(u=f,c=d))}return u=(u=(u=u||i[i.length-1]).from<s?{from:s,to:u.to,level:u.level}:u).to>a?{from:u.from,to:a,level:u.level}:u}function Xn(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==kn){kn=M("pre",null,"CodeMirror-line-like");for(var t=0;t<49;++t)kn.appendChild(document.createTextNode("x")),kn.appendChild(M("br"));kn.appendChild(document.createTextNode("x"))}L(e.measure,kn);var n=kn.offsetHeight/50;return 3<n&&(e.cachedTextHeight=n),S(e.measure),n||1}function Yn(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=M("span","xxxxxxxxxx"),n=M("pre",[t],"CodeMirror-line-like");L(e.measure,n);t=t.getBoundingClientRect(),t=(t.right-t.left)/10;return 2<t&&(e.cachedCharWidth=t),t||10}function $n(e){for(var t=e.display,n={},r={},i=t.gutters.clientLeft,o=t.gutters.firstChild,l=0;o;o=o.nextSibling,++l){var s=e.display.gutterSpecs[l].className;n[s]=o.offsetLeft+o.clientLeft+i,r[s]=o.clientWidth}return{fixedPos:_n(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:n,gutterWidth:r,wrapperWidth:t.wrapper.clientWidth}}function _n(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function qn(r){var i=Xn(r.display),o=r.options.lineWrapping,l=o&&Math.max(5,r.display.scroller.clientWidth/Yn(r.display)-3);return function(e){if(zt(r.doc,e))return 0;var t=0;if(e.widgets)for(var n=0;n<e.widgets.length;n++)e.widgets[n].height&&(t+=e.widgets[n].height);return o?t+(Math.ceil(e.text.length/l)||1)*i:t+i}}function Zn(e){var t=e.doc,n=qn(e);t.iter(function(e){var t=n(e);t!=e.height&&_e(e,t)})}function Qn(e,t,n,r){var i=e.display;if(!n&&"true"==Me(t).getAttribute("cm-not-content"))return null;var o,i=i.lineSpace.getBoundingClientRect();try{o=t.clientX-i.left,s=t.clientY-i.top}catch(e){return null}var l,s=Bn(e,o,s);return r&&0<s.xRel&&(l=Xe(e.doc,s.line).text).length==s.ch&&(l=P(l,l.length,e.options.tabSize)-l.length,s=et(s.line,Math.max(0,Math.round((o-mn(e.display).left)/Yn(e.display))-l))),s}function Jn(e,t){if(t>=e.display.viewTo)return null;if((t-=e.display.viewFrom)<0)return null;for(var n=e.display.view,r=0;r<n.length;r++)if((t-=n[r].size)<0)return r}function er(e,t,n,r){null==t&&(t=e.doc.first),null==n&&(n=e.doc.first+e.doc.size);var i,o,l=e.display;(r=r||0)&&n<l.viewTo&&(null==l.updateLineNumbers||l.updateLineNumbers>t)&&(l.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=l.viewTo?Ct&&It(e.doc,t)<l.viewTo&&nr(e):n<=l.viewFrom?Ct&&Rt(e.doc,n+r)>l.viewFrom?nr(e):(l.viewFrom+=r,l.viewTo+=r):t<=l.viewFrom&&n>=l.viewTo?nr(e):t<=l.viewFrom?(i=rr(e,n,n+r,1))?(l.view=l.view.slice(i.index),l.viewFrom=i.lineN,l.viewTo+=r):nr(e):n>=l.viewTo?(o=rr(e,t,t,-1))?(l.view=l.view.slice(0,o.index),l.viewTo=o.lineN):nr(e):(i=rr(e,t,t,-1),o=rr(e,n,n+r,1),i&&o?(l.view=l.view.slice(0,i.index).concat(Qt(e,i.lineN,o.lineN)).concat(l.view.slice(o.index)),l.viewTo+=r):nr(e));e=l.externalMeasured;e&&(n<e.lineN?e.lineN+=r:t<e.lineN+e.size&&(l.externalMeasured=null))}function tr(e,t,n){e.curOp.viewChanged=!0;var r=e.display,i=e.display.externalMeasured;i&&t>=i.lineN&&t<i.lineN+i.size&&(r.externalMeasured=null),t<r.viewFrom||t>=r.viewTo||(null==(t=r.view[Jn(e,t)]).node||-1==I(t=t.changes||(t.changes=[]),n)&&t.push(n))}function nr(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function rr(e,t,n,r){var i,o=Jn(e,t),l=e.display.view;if(!Ct||n==e.doc.first+e.doc.size)return{index:o,lineN:n};for(var s=e.display.viewFrom,a=0;a<o;a++)s+=l[a].size;if(s!=t){if(0<r){if(o==l.length-1)return null;i=s+l[o].size-t,o++}else i=s-t;t+=i,n+=i}for(;It(e.doc,n)!=n;){if(o==(r<0?0:l.length-1))return null;n+=r*l[o-(r<0?1:0)].size,o+=r}return{index:o,lineN:n}}function ir(e){for(var t=e.display.view,n=0,r=0;r<t.length;r++){var i=t[r];i.hidden||i.node&&!i.changes||++n}return n}function or(e){e.display.input.showSelection(e.display.input.prepareSelection())}function lr(e,t){void 0===t&&(t=!0);var n=e.doc,r={},i=r.cursors=document.createDocumentFragment(),o=r.selection=document.createDocumentFragment(),l=e.options.$customCursor;l&&(t=!0);for(var s,a,u,c=0;c<n.sel.ranges.length;c++)!t&&c==n.sel.primIndex||((s=n.sel.ranges[c]).from().line>=e.display.viewTo||s.to().line<e.display.viewFrom||(a=s.empty(),l?(u=l(e,s))&&sr(e,u,i):(a||e.options.showCursorWhenSelecting)&&sr(e,s.head,i),a||function(i,e,t){var n=i.display,o=i.doc,l=document.createDocumentFragment(),r=mn(i.display),S=r.left,L=Math.max(n.sizerWidth,yn(i)-n.sizer.offsetLeft)-r.right,k="ltr"==o.direction;function T(e,t,n,r){t<0&&(t=0),t=Math.round(t),r=Math.round(r),l.appendChild(M("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==n?L-e:n)+"px;\n                             height: "+(r-t)+"px"))}function s(n,g,m){var v,y,r=Xe(o,n),b=r.text.length;function w(e,t){return En(i,et(n,e),"div",r,t)}function x(e,t,n){e=Un(i,r,null,e),t="ltr"==t==("after"==n)?"left":"right";return w("after"==n?e.begin:e.end-(/\s/.test(r.text.charAt(e.end-1))?2:1),t)[t]}var C=fe(r,o.direction);return function(e,t,n,r){if(!e)return r(t,n,"ltr",0);for(var i=!1,o=0;o<e.length;++o){var l=e[o];(l.from<n&&l.to>t||t==n&&l.to==t)&&(r(Math.max(l.from,t),Math.min(l.to,n),1==l.level?"rtl":"ltr",o),i=!0)}i||r(t,n,"ltr")}(C,g||0,null==m?b:m,function(e,t,n,r){var i,o,l,s,a="ltr"==n,u=w(e,a?"left":"right"),c=w(t-1,a?"right":"left"),h=null==g&&0==e,d=null==m&&t==b,f=0==r,p=!C||r==C.length-1;c.top-u.top<=3?(i=(k?h:d)&&f?S:(a?u:c).left,r=(k?d:h)&&p?L:(a?c:u).right,T(i,u.top,r-i,u.bottom)):(n=a?(o=k&&h&&f?S:u.left,l=k?L:x(e,n,"before"),s=k?S:x(t,n,"after"),k&&d&&p?L:c.right):(o=k?x(e,n,"before"):S,l=!k&&h&&f?L:u.right,s=!k&&d&&p?S:c.left,k?x(t,n,"after"):L),T(o,u.top,l-o,u.bottom),u.bottom<c.top&&T(S,u.bottom,null,c.top),T(s,c.top,n-s,c.bottom)),(!v||ar(u,v)<0)&&(v=u),ar(c,v)<0&&(v=c),(!y||ar(u,y)<0)&&(y=u),ar(c,y)<0&&(y=c)}),{start:v,end:y}}var a=e.from(),n=e.to();a.line==n.line?s(a.line,a.ch,n.ch):(r=Xe(o,a.line),e=Xe(o,n.line),e=Et(r)==Et(e),r=s(a.line,a.ch,e?r.text.length+1:null).end,n=s(n.line,e?0:null,n.ch).start,e&&(r.top<n.top-2?(T(r.right,r.top,null,r.bottom),T(S,n.top,n.left,n.bottom)):T(r.right,r.top,n.left-r.right,r.bottom)),r.bottom<n.top&&T(S,r.bottom,null,n.top));t.appendChild(l)}(e,s,o)));return r}function sr(e,t,n){var r=In(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=n.appendChild(M("div"," ","CodeMirror-cursor"));i.style.left=r.left+"px",i.style.top=r.top+"px",i.style.height=Math.max(0,r.bottom-r.top)*e.options.cursorHeight+"px",/\bcm-fat-cursor\b/.test(e.getWrapperElement().className)&&(t=(t=En(e,t,"div",null,null)).right-t.left,i.style.width=(0<t?t:e.defaultCharWidth())+"px"),r.other&&((n=n.appendChild(M("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"))).style.display="",n.style.left=r.other.left+"px",n.style.top=r.other.top+"px",n.style.height=.85*(r.other.bottom-r.other.top)+"px")}function ar(e,t){return e.top-t.top||e.left-t.left}function ur(e){var t,n;e.state.focused&&(t=e.display,clearInterval(t.blinker),n=!0,t.cursorDiv.style.visibility="",0<e.options.cursorBlinkRate?t.blinker=setInterval(function(){e.hasFocus()||fr(e),t.cursorDiv.style.visibility=(n=!n)?"":"hidden"},e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden"))}function cr(e){e.hasFocus()||(e.display.input.focus(),e.state.focused||dr(e))}function hr(e){e.state.delayingBlurEvent=!0,setTimeout(function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,e.state.focused&&fr(e))},100)}function dr(e,t){e.state.delayingBlurEvent&&!e.state.draggingText&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(ye(e,"focus",e,t),e.state.focused=!0,O(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),f&&setTimeout(function(){return e.display.input.reset(!0)},20)),e.display.input.receivedFocus()),ur(e))}function fr(e,t){e.state.delayingBlurEvent||(e.state.focused&&(ye(e,"blur",e,t),e.state.focused=!1,C(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout(function(){e.state.focused||(e.display.shift=!1)},150))}function pr(e){for(var t=e.display,n=t.lineDiv.offsetTop,r=Math.max(0,t.scroller.getBoundingClientRect().top),i=t.lineDiv.getBoundingClientRect().top,o=0,l=0;l<t.view.length;l++){var s,a=t.view[l],u=e.options.lineWrapping,c=void 0,h=0;if(!a.hidden){i+=a.line.height,w&&v<8?(c=(s=a.node.offsetTop+a.node.offsetHeight)-n,n=s):(c=(d=a.node.getBoundingClientRect()).bottom-d.top,!u&&a.text.firstChild&&(h=a.text.firstChild.getBoundingClientRect().right-d.left-1));var d=a.line.height-c;if((.005<d||d<-.005)&&(i<r&&(o-=d),_e(a.line,c),gr(a.line),a.rest))for(var f=0;f<a.rest.length;f++)gr(a.rest[f]);h>e.display.sizerWidth&&((h=Math.ceil(h/Yn(e.display)))>e.display.maxLineLength&&(e.display.maxLineLength=h,e.display.maxLine=a.line,e.display.maxLineChanged=!0))}}2<Math.abs(o)&&(t.scroller.scrollTop+=o)}function gr(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var n=e.widgets[t],r=n.node.parentNode;r&&(n.height=r.offsetHeight)}}function mr(e,t,n){var r=n&&null!=n.top?Math.max(0,n.top):e.scroller.scrollTop,r=Math.floor(r-pn(e)),i=n&&null!=n.bottom?n.bottom:r+e.wrapper.clientHeight,o=Ze(t,r),r=Ze(t,i);return n&&n.ensure&&(i=n.ensure.from.line,n=n.ensure.to.line,i<o?r=Ze(t,Bt(Xe(t,o=i))+e.wrapper.clientHeight):Math.min(n,t.lastLine())>=r&&(o=Ze(t,Bt(Xe(t,n))-e.wrapper.clientHeight),r=n)),{from:o,to:Math.max(r,o+1)}}function vr(e,t){var n=e.display,r=Xn(e.display);t.top<0&&(t.top=0);var i=(e.curOp&&null!=e.curOp.scrollTop?e.curOp:n.scroller).scrollTop,o=bn(e),l={};t.bottom-t.top>o&&(t.bottom=t.top+o);var s=e.doc.height+gn(n),a=t.top<r,r=t.bottom>s-r;t.top<i?l.scrollTop=a?0:t.top:t.bottom>i+o&&((u=Math.min(t.top,(r?s:t.bottom)-o))!=i&&(l.scrollTop=u));var i=e.options.fixedGutter?0:n.gutters.offsetWidth,u=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:n.scroller.scrollLeft-i,e=yn(e)-n.gutters.offsetWidth,n=t.right-t.left>e;return n&&(t.right=t.left+e),t.left<10?l.scrollLeft=0:t.left<u?l.scrollLeft=Math.max(0,t.left+i-(n?0:10)):t.right>e+u-3&&(l.scrollLeft=t.right+(n?0:10)-e),l}function yr(e,t){null!=t&&(xr(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc:e.curOp).scrollTop+t)}function br(e){xr(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function wr(e,t,n){null==t&&null==n||xr(e),null!=t&&(e.curOp.scrollLeft=t),null!=n&&(e.curOp.scrollTop=n)}function xr(e){var t=e.curOp.scrollToPos;t&&(e.curOp.scrollToPos=null,Cr(e,Rn(e,t.from),Rn(e,t.to),t.margin))}function Cr(e,t,n,r){r=vr(e,{left:Math.min(t.left,n.left),top:Math.min(t.top,n.top)-r,right:Math.max(t.right,n.right),bottom:Math.max(t.bottom,n.bottom)+r});wr(e,r.scrollLeft,r.scrollTop)}function Sr(e,t){Math.abs(e.doc.scrollTop-t)<2||(d||jr(e,{top:t}),Lr(e,t,!0),d&&jr(e),Br(e,100))}function Lr(e,t,n){t=Math.max(0,Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t)),e.display.scroller.scrollTop==t&&!n||(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function kr(e,t,n,r){t=Math.max(0,Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth)),(n?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!r||(e.doc.scrollLeft=t,$r(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function Tr(e){var t=e.display,n=t.gutters.offsetWidth,r=Math.round(e.doc.height+gn(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?n:0,docHeight:r,scrollHeight:r+vn(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:n}}function Mr(e,t,n){this.cm=n;var r=this.vert=M("div",[M("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=M("div",[M("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");r.tabIndex=i.tabIndex=-1,e(r),e(i),ge(r,"scroll",function(){r.clientHeight&&t(r.scrollTop,"vertical")}),ge(i,"scroll",function(){i.clientWidth&&t(i.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,w&&v<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")}Mr.prototype.update=function(e){var t,n=e.scrollWidth>e.clientWidth+1,r=e.scrollHeight>e.clientHeight+1,i=e.nativeBarWidth;return r?(this.vert.style.display="block",this.vert.style.bottom=n?i+"px":"0",t=e.viewHeight-(n?i:0),this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+t)+"px"):(this.vert.scrollTop=0,this.vert.style.display="",this.vert.firstChild.style.height="0"),n?(this.horiz.style.display="block",this.horiz.style.right=r?i+"px":"0",this.horiz.style.left=e.barLeft+"px",t=e.viewWidth-e.barLeft-(r?i:0),this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+t)+"px"):(this.horiz.style.display="",this.horiz.firstChild.style.width="0"),!this.checkedZeroWidth&&0<e.clientHeight&&(0==i&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:r?i:0,bottom:n?i:0}},Mr.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},Mr.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},Mr.prototype.zeroWidthHack=function(){this.horiz.style.height=this.vert.style.width=g&&!l?"12px":"18px",this.horiz.style.pointerEvents=this.vert.style.pointerEvents="none",this.disableHoriz=new E,this.disableVert=new E},Mr.prototype.enableZeroWidthBar=function(n,r,i){n.style.pointerEvents="auto",r.set(1e3,function e(){var t=n.getBoundingClientRect();("vert"==i?document.elementFromPoint(t.right-1,(t.top+t.bottom)/2):document.elementFromPoint((t.right+t.left)/2,t.bottom-1))!=n?n.style.pointerEvents="none":r.set(1e3,e)})},Mr.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};function Nr(){}function Or(e,t){t=t||Tr(e);var n=e.display.barWidth,r=e.display.barHeight;Ar(e,t);for(var i=0;i<4&&n!=e.display.barWidth||r!=e.display.barHeight;i++)n!=e.display.barWidth&&e.options.lineWrapping&&pr(e),Ar(e,Tr(e)),n=e.display.barWidth,r=e.display.barHeight}function Ar(e,t){var n=e.display,r=n.scrollbars.update(t);n.sizer.style.paddingRight=(n.barWidth=r.right)+"px",n.sizer.style.paddingBottom=(n.barHeight=r.bottom)+"px",n.heightForcer.style.borderBottom=r.bottom+"px solid transparent",r.right&&r.bottom?(n.scrollbarFiller.style.display="block",n.scrollbarFiller.style.height=r.bottom+"px",n.scrollbarFiller.style.width=r.right+"px"):n.scrollbarFiller.style.display="",r.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(n.gutterFiller.style.display="block",n.gutterFiller.style.height=r.bottom+"px",n.gutterFiller.style.width=t.gutterWidth+"px"):n.gutterFiller.style.display=""}Nr.prototype.update=function(){return{bottom:0,right:0}},Nr.prototype.setScrollLeft=function(){},Nr.prototype.setScrollTop=function(){},Nr.prototype.clear=function(){};var Dr={native:Mr,null:Nr};function Wr(n){n.display.scrollbars&&(n.display.scrollbars.clear(),n.display.scrollbars.addClass&&C(n.display.wrapper,n.display.scrollbars.addClass)),n.display.scrollbars=new Dr[n.options.scrollbarStyle](function(e){n.display.wrapper.insertBefore(e,n.display.scrollbarFiller),ge(e,"mousedown",function(){n.state.focused&&setTimeout(function(){return n.display.input.focus()},0)}),e.setAttribute("cm-not-content","true")},function(e,t){("horizontal"==t?kr:Sr)(n,e)},n),n.display.scrollbars.addClass&&O(n.display.wrapper,n.display.scrollbars.addClass)}var Hr=0;function Fr(e){e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++Hr,markArrays:null},e=e.curOp,Jt?Jt.ops.push(e):e.ownsGroup=Jt={ops:[e],delayedCallbacks:[]}}function Pr(e){e=e.curOp;e&&en(e,function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;!function(e){for(var t=e.ops,n=0;n<t.length;n++)!function(e){var t=e.cm,n=t.display;(function(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=vn(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=vn(e)+"px",t.scrollbarsClipped=!0)})(t),e.updateMaxLine&&Ut(t);e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<n.viewFrom||e.scrollToPos.to.line>=n.viewTo)||n.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new Ur(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}(t[n]);for(var r=0;r<t.length;r++)!function(e){e.updatedDisplay=e.mustUpdate&&Vr(e.cm,e.update)}(t[r]);for(var i=0;i<t.length;i++)!function(e){var t=e.cm,n=t.display;e.updatedDisplay&&pr(t);e.barMeasure=Tr(t),n.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=xn(t,n.maxLine,n.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(n.scroller.clientWidth,n.sizer.offsetLeft+e.adjustWidthTo+vn(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,n.sizer.offsetLeft+e.adjustWidthTo-yn(t)));(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=n.input.prepareSelection())}(t[i]);for(var o=0;o<t.length;o++)!function(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&kr(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var n=e.focus&&e.focus==N();e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,n);!e.updatedDisplay&&e.startHeight==t.doc.height||Or(t,e.barMeasure);e.updatedDisplay&&Yr(t,e.barMeasure);e.selectionChanged&&ur(t);t.state.focused&&e.updateInput&&t.display.input.reset(e.typing);n&&cr(e.cm)}(t[o]);for(var l=0;l<t.length;l++)!function(e){var t=e.cm,n=t.display,r=t.doc;e.updatedDisplay&&Kr(t,e.update);null==n.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(n.wheelStartX=n.wheelStartY=null);null!=e.scrollTop&&Lr(t,e.scrollTop,e.forceScroll);null!=e.scrollLeft&&kr(t,e.scrollLeft,!0,!0);{var i;e.scrollToPos&&(i=function(e,t,n,r){null==r&&(r=0),e.options.lineWrapping||t!=n||(n="before"==t.sticky?et(t.line,t.ch+1,"before"):t,t=t.ch?et(t.line,"before"==t.sticky?t.ch-1:t.ch,"after"):t);for(var i=0;i<5;i++){var o,l=!1,s=In(e,t),a=n&&n!=t?In(e,n):s,u=vr(e,o={left:Math.min(s.left,a.left),top:Math.min(s.top,a.top)-r,right:Math.max(s.left,a.left),bottom:Math.max(s.bottom,a.bottom)+r}),s=e.doc.scrollTop,a=e.doc.scrollLeft;if(null!=u.scrollTop&&(Sr(e,u.scrollTop),1<Math.abs(e.doc.scrollTop-s)&&(l=!0)),null!=u.scrollLeft&&(kr(e,u.scrollLeft),1<Math.abs(e.doc.scrollLeft-a)&&(l=!0)),!l)break}return o}(t,st(r,e.scrollToPos.from),st(r,e.scrollToPos.to),e.scrollToPos.margin),function(e,t){var n,r,i;be(e,"scrollCursorIntoView")||(r=(n=e.display).sizer.getBoundingClientRect(),i=null,t.top+r.top<0?i=!0:t.bottom+r.top>(window.innerHeight||document.documentElement.clientHeight)&&(i=!1),null==i||u||(t=M("div","​",null,"position: absolute;\n                         top: "+(t.top-n.viewOffset-pn(e.display))+"px;\n                         height: "+(t.bottom-t.top+vn(e)+n.barHeight)+"px;\n                         left: "+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;"),e.display.lineSpace.appendChild(t),t.scrollIntoView(i),e.display.lineSpace.removeChild(t)))}(t,i))}var o=e.maybeHiddenMarkers,l=e.maybeUnhiddenMarkers;if(o)for(var s=0;s<o.length;++s)o[s].lines.length||ye(o[s],"hide");if(l)for(var a=0;a<l.length;++a)l[a].lines.length&&ye(l[a],"unhide");n.wrapper.offsetHeight&&(r.scrollTop=t.display.scroller.scrollTop);e.changeObjs&&ye(t,"changes",t,e.changeObjs);e.update&&e.update.finish()}(t[l])}(e)})}function Er(e,t){if(e.curOp)return t();Fr(e);try{return t()}finally{Pr(e)}}function Ir(e,t){return function(){if(e.curOp)return t.apply(e,arguments);Fr(e);try{return t.apply(e,arguments)}finally{Pr(e)}}}function Rr(e){return function(){if(this.curOp)return e.apply(this,arguments);Fr(this);try{return e.apply(this,arguments)}finally{Pr(this)}}}function zr(t){return function(){var e=this.cm;if(!e||e.curOp)return t.apply(this,arguments);Fr(e);try{return t.apply(this,arguments)}finally{Pr(e)}}}function Br(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,H(Gr,e))}function Gr(l){var s,a,u,c=l.doc;c.highlightFrontier>=l.display.viewTo||(s=+new Date+l.options.workTime,a=ft(l,c.highlightFrontier),u=[],c.iter(a.line,Math.min(c.first+c.size,l.display.viewTo+500),function(e){if(a.line>=l.display.viewFrom){var t=e.styles,n=e.text.length>l.options.maxHighlightLength?Ue(c.mode,a.state):null,r=ht(l,e,a,!0);n&&(a.state=n),e.styles=r.styles;n=e.styleClasses,r=r.classes;r?e.styleClasses=r:n&&(e.styleClasses=null);for(var i=!t||t.length!=e.styles.length||n!=r&&(!n||!r||n.bgClass!=r.bgClass||n.textClass!=r.textClass),o=0;!i&&o<t.length;++o)i=t[o]!=e.styles[o];i&&u.push(a.line),e.stateAfter=a.save(),a.nextLine()}else e.text.length<=l.options.maxHighlightLength&&pt(l,e.text,a),e.stateAfter=a.line%5==0?a.save():null,a.nextLine();if(+new Date>s)return Br(l,l.options.workDelay),!0}),c.highlightFrontier=a.line,c.modeFrontier=Math.max(c.modeFrontier,a.line),u.length&&Er(l,function(){for(var e=0;e<u.length;e++)tr(l,u[e],"text")}))}var Ur=function(e,t,n){var r=e.display;this.viewport=t,this.visible=mr(r,e.doc,t),this.editorIsHidden=!r.wrapper.offsetWidth,this.wrapperHeight=r.wrapper.clientHeight,this.wrapperWidth=r.wrapper.clientWidth,this.oldDisplayWidth=yn(e),this.force=n,this.dims=$n(e),this.events=[]};function Vr(e,t){var n=e.display,r=e.doc;if(t.editorIsHidden)return nr(e),!1;if(!t.force&&t.visible.from>=n.viewFrom&&t.visible.to<=n.viewTo&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo)&&n.renderedView==n.view&&0==ir(e))return!1;_r(e)&&(nr(e),t.dims=$n(e));var i=r.first+r.size,o=Math.max(t.visible.from-e.options.viewportMargin,r.first),l=Math.min(i,t.visible.to+e.options.viewportMargin);n.viewFrom<o&&o-n.viewFrom<20&&(o=Math.max(r.first,n.viewFrom)),n.viewTo>l&&n.viewTo-l<20&&(l=Math.min(i,n.viewTo)),Ct&&(o=It(e.doc,o),l=Rt(e.doc,l));var s=o!=n.viewFrom||l!=n.viewTo||n.lastWrapHeight!=t.wrapperHeight||n.lastWrapWidth!=t.wrapperWidth;r=o,i=l,0==(l=(o=e).display).view.length||r>=l.viewTo||i<=l.viewFrom?(l.view=Qt(o,r,i),l.viewFrom=r):(l.viewFrom>r?l.view=Qt(o,r,l.viewFrom).concat(l.view):l.viewFrom<r&&(l.view=l.view.slice(Jn(o,r))),l.viewFrom=r,l.viewTo<i?l.view=l.view.concat(Qt(o,l.viewTo,i)):l.viewTo>i&&(l.view=l.view.slice(0,Jn(o,i)))),l.viewTo=i,n.viewOffset=Bt(Xe(e.doc,n.viewFrom)),e.display.mover.style.top=n.viewOffset+"px";o=ir(e);if(!s&&0==o&&!t.force&&n.renderedView==n.view&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo))return!1;var l=function(e){if(e.hasFocus())return null;var t=N();if(!t||!T(e.display.lineDiv,t))return null;var n={activeElt:t};return!window.getSelection||(t=window.getSelection()).anchorNode&&t.extend&&T(e.display.lineDiv,t.anchorNode)&&(n.anchorNode=t.anchorNode,n.anchorOffset=t.anchorOffset,n.focusNode=t.focusNode,n.focusOffset=t.focusOffset),n}(e);return 4<o&&(n.lineDiv.style.display="none"),function(n,e,t){var r=n.display,i=n.options.lineNumbers,o=r.lineDiv,l=o.firstChild;function s(e){var t=e.nextSibling;return f&&g&&n.display.currentWheelTarget==e?e.style.display="none":e.parentNode.removeChild(e),t}for(var a=r.view,u=r.viewFrom,c=0;c<a.length;c++){var h=a[c];if(!h.hidden)if(h.node&&h.node.parentNode==o){for(;l!=h.node;)l=s(l);var d=i&&null!=e&&e<=u&&h.lineNumber;h.changes&&(-1<I(h.changes,"gutter")&&(d=!1),on(n,h,u,t)),d&&(S(h.lineNumber),h.lineNumber.appendChild(document.createTextNode(Je(n.options,u)))),l=h.node.nextSibling}else{d=function(e,t,n,r){var i=sn(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),an(e,t),un(e,t,n,r),cn(e,t,r),t.node}(n,h,u,t);o.insertBefore(d,l)}u+=h.size}for(;l;)l=s(l)}(e,n.updateLineNumbers,t.dims),4<o&&(n.lineDiv.style.display=""),n.renderedView=n.view,(i=l)&&i.activeElt&&i.activeElt!=N()&&(i.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(i.activeElt.nodeName)&&i.anchorNode&&T(document.body,i.anchorNode)&&T(document.body,i.focusNode)&&(o=window.getSelection(),(l=document.createRange()).setEnd(i.anchorNode,i.anchorOffset),l.collapse(!1),o.removeAllRanges(),o.addRange(l),o.extend(i.focusNode,i.focusOffset))),S(n.cursorDiv),S(n.selectionDiv),n.gutters.style.height=n.sizer.style.minHeight=0,s&&(n.lastWrapHeight=t.wrapperHeight,n.lastWrapWidth=t.wrapperWidth,Br(e,400)),!(n.updateLineNumbers=null)}function Kr(e,t){for(var n=t.viewport,r=!0;;r=!1){if(r&&e.options.lineWrapping&&t.oldDisplayWidth!=yn(e))r&&(t.visible=mr(e.display,e.doc,n));else if(n&&null!=n.top&&(n={top:Math.min(e.doc.height+gn(e.display)-bn(e),n.top)}),t.visible=mr(e.display,e.doc,n),t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)break;if(!Vr(e,t))break;pr(e);var i=Tr(e);or(e),Or(e,i),Yr(e,i),t.force=!1}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function jr(e,t){var n=new Ur(e,t);Vr(e,n)&&(pr(e),Kr(e,n),t=Tr(e),or(e),Or(e,t),Yr(e,t),n.finish())}function Xr(e){var t=e.gutters.offsetWidth;e.sizer.style.marginLeft=t+"px",nn(e,"gutterChanged",e)}function Yr(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+vn(e)+"px"}function $r(e){var t=e.display,n=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var r=_n(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=r+"px",l=0;l<n.length;l++)if(!n[l].hidden){e.options.fixedGutter&&(n[l].gutter&&(n[l].gutter.style.left=o),n[l].gutterBackground&&(n[l].gutterBackground.style.left=o));var s=n[l].alignable;if(s)for(var a=0;a<s.length;a++)s[a].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=r+i+"px")}}function _r(e){if(e.options.lineNumbers){var t=e.doc,n=Je(e.options,t.first+t.size-1),r=e.display;if(n.length!=r.lineNumChars){var i=r.measure.appendChild(M("div",[M("div",n)],"CodeMirror-linenumber CodeMirror-gutter-elt")),t=i.firstChild.offsetWidth,i=i.offsetWidth-t;return r.lineGutter.style.width="",r.lineNumInnerWidth=Math.max(t,r.lineGutter.offsetWidth-i)+1,r.lineNumWidth=r.lineNumInnerWidth+i,r.lineNumChars=r.lineNumInnerWidth?n.length:-1,r.lineGutter.style.width=r.lineNumWidth+"px",Xr(e.display),1}}}function qr(e,t){for(var n=[],r=!1,i=0;i<e.length;i++){var o=e[i],l=null;if("string"!=typeof o&&(l=o.style,o=o.className),"CodeMirror-linenumbers"==o){if(!t)continue;r=!0}n.push({className:o,style:l})}return t&&!r&&n.push({className:"CodeMirror-linenumbers",style:null}),n}function Zr(e){var t=e.gutters,n=e.gutterSpecs;S(t),e.lineGutter=null;for(var r=0;r<n.length;++r){var i=n[r],o=i.className,l=i.style,i=t.appendChild(M("div",null,"CodeMirror-gutter "+o));l&&(i.style.cssText=l),"CodeMirror-linenumbers"==o&&((e.lineGutter=i).style.width=(e.lineNumWidth||1)+"px")}t.style.display=n.length?"":"none",Xr(e)}function Qr(e){Zr(e.display),er(e),$r(e)}function Jr(e,t,n,r){var i=this;this.input=n,i.scrollbarFiller=M("div",null,"CodeMirror-scrollbar-filler"),i.scrollbarFiller.setAttribute("cm-not-content","true"),i.gutterFiller=M("div",null,"CodeMirror-gutter-filler"),i.gutterFiller.setAttribute("cm-not-content","true"),i.lineDiv=k("div",null,"CodeMirror-code"),i.selectionDiv=M("div",null,null,"position: relative; z-index: 1"),i.cursorDiv=M("div",null,"CodeMirror-cursors"),i.measure=M("div",null,"CodeMirror-measure"),i.lineMeasure=M("div",null,"CodeMirror-measure"),i.lineSpace=k("div",[i.measure,i.lineMeasure,i.selectionDiv,i.cursorDiv,i.lineDiv],null,"position: relative; outline: none");var o=k("div",[i.lineSpace],"CodeMirror-lines");i.mover=M("div",[o],null,"position: relative"),i.sizer=M("div",[i.mover],"CodeMirror-sizer"),i.sizerWidth=null,i.heightForcer=M("div",null,null,"position: absolute; height: "+R+"px; width: 1px;"),i.gutters=M("div",null,"CodeMirror-gutters"),i.lineGutter=null,i.scroller=M("div",[i.sizer,i.heightForcer,i.gutters],"CodeMirror-scroll"),i.scroller.setAttribute("tabIndex","-1"),i.wrapper=M("div",[i.scrollbarFiller,i.gutterFiller,i.scroller],"CodeMirror"),i.wrapper.setAttribute("translate","no"),w&&v<8&&(i.gutters.style.zIndex=-1,i.scroller.style.paddingRight=0),f||d&&h||(i.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(i.wrapper):e(i.wrapper)),i.viewFrom=i.viewTo=t.first,i.reportedViewFrom=i.reportedViewTo=t.first,i.view=[],i.renderedView=null,i.externalMeasured=null,i.viewOffset=0,i.lastWrapHeight=i.lastWrapWidth=0,i.updateLineNumbers=null,i.nativeBarWidth=i.barHeight=i.barWidth=0,i.scrollbarsClipped=!1,i.lineNumWidth=i.lineNumInnerWidth=i.lineNumChars=null,i.alignWidgets=!1,i.cachedCharWidth=i.cachedTextHeight=i.cachedPaddingH=null,i.maxLine=null,i.maxLineLength=0,i.maxLineChanged=!1,i.wheelDX=i.wheelDY=i.wheelStartX=i.wheelStartY=null,i.shift=!1,i.selForContextMenu=null,i.activeTouch=null,i.gutterSpecs=qr(r.gutters,r.lineNumbers),Zr(i),n.init(i)}Ur.prototype.signal=function(e,t){xe(e,t)&&this.events.push(arguments)},Ur.prototype.finish=function(){for(var e=0;e<this.events.length;e++)ye.apply(null,this.events[e])};var ei=0,ti=null;function ni(e){var t=e.wheelDeltaX,n=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==n&&e.detail&&e.axis==e.VERTICAL_AXIS?n=e.detail:null==n&&(n=e.wheelDelta),{x:t,y:n}}function ri(e){e=ni(e);return e.x*=ti,e.y*=ti,e}function ii(e,t){var n=ni(t),r=n.x,i=n.y,o=ti;0===t.deltaMode&&(r=t.deltaX,i=t.deltaY,o=1);var l=e.display,s=l.scroller,a=s.scrollWidth>s.clientWidth,n=s.scrollHeight>s.clientHeight;if(r&&a||i&&n){if(i&&g&&f)e:for(var u=t.target,c=l.view;u!=s;u=u.parentNode)for(var h=0;h<c.length;h++)if(c[h].node==u){e.display.currentWheelTarget=u;break e}if(r&&!d&&!p&&null!=o)return i&&n&&Sr(e,Math.max(0,s.scrollTop+i*o)),kr(e,Math.max(0,s.scrollLeft+r*o)),i&&!n||Se(t),void(l.wheelStartX=null);i&&null!=o&&(n=(a=e.doc.scrollTop)+l.wrapper.clientHeight,(o=i*o)<0?a=Math.max(0,a+o-50):n=Math.min(e.doc.height,n+o+50),jr(e,{top:a,bottom:n})),ei<20&&0!==t.deltaMode&&(null==l.wheelStartX?(l.wheelStartX=s.scrollLeft,l.wheelStartY=s.scrollTop,l.wheelDX=r,l.wheelDY=i,setTimeout(function(){var e,t;null!=l.wheelStartX&&(t=s.scrollLeft-l.wheelStartX,t=(e=s.scrollTop-l.wheelStartY)&&l.wheelDY&&e/l.wheelDY||t&&l.wheelDX&&t/l.wheelDX,l.wheelStartX=l.wheelStartY=null,t&&(ti=(ti*ei+t)/(ei+1),++ei))},200)):(l.wheelDX+=r,l.wheelDY+=i))}}w?ti=-.53:d?ti=15:o?ti=-.7:c&&(ti=-1/3);var oi=function(e,t){this.ranges=e,this.primIndex=t};oi.prototype.primary=function(){return this.ranges[this.primIndex]},oi.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var n=this.ranges[t],r=e.ranges[t];if(!nt(n.anchor,r.anchor)||!nt(n.head,r.head))return!1}return!0},oi.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new li(rt(this.ranges[t].anchor),rt(this.ranges[t].head));return new oi(e,this.primIndex)},oi.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},oi.prototype.contains=function(e,t){t=t||e;for(var n=0;n<this.ranges.length;n++){var r=this.ranges[n];if(0<=tt(t,r.from())&&tt(e,r.to())<=0)return n}return-1};var li=function(e,t){this.anchor=e,this.head=t};function si(e,t,n){var r=e&&e.options.selectionsMayTouch,e=t[n];t.sort(function(e,t){return tt(e.from(),t.from())}),n=I(t,e);for(var i=1;i<t.length;i++){var o,l=t[i],s=t[i-1],a=tt(s.to(),l.from());(r&&!l.empty()?0<a:0<=a)&&(o=ot(s.from(),l.from()),a=it(s.to(),l.to()),s=s.empty()?l.from()==l.head:s.from()==s.head,i<=n&&--n,t.splice(--i,2,new li(s?a:o,s?o:a)))}return new oi(t,n)}function ai(e,t){return new oi([new li(e,t||e)],0)}function ui(e){return e.text?et(e.from.line+e.text.length-1,X(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function ci(e,t){if(tt(e,t.from)<0)return e;if(tt(e,t.to)<=0)return ui(t);var n=e.line+t.text.length-(t.to.line-t.from.line)-1,r=e.ch;return e.line==t.to.line&&(r+=ui(t).ch-t.to.ch),et(n,r)}function hi(e,t){for(var n=[],r=0;r<e.sel.ranges.length;r++){var i=e.sel.ranges[r];n.push(new li(ci(i.anchor,t),ci(i.head,t)))}return si(e.cm,n,e.sel.primIndex)}function di(e,t,n){return e.line==t.line?et(n.line,e.ch-t.ch+n.ch):et(n.line+(e.line-t.line),e.ch)}function fi(e){e.doc.mode=ze(e.options,e.doc.modeOption),pi(e)}function pi(e){e.doc.iter(function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)}),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,Br(e,100),e.state.modeGen++,e.curOp&&er(e)}function gi(e,t){return 0==t.from.ch&&0==t.to.ch&&""==X(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function mi(e,o,t,l){function i(e){return t?t[e]:null}function n(e,t,n){var r,i;r=n,i=l,(n=e).text=t,n.stateAfter&&(n.stateAfter=null),n.styles&&(n.styles=null),null!=n.order&&(n.order=null),Mt(n),Nt(n,r),(i=i?i(n):1)!=n.height&&_e(n,i),nn(e,"change",e,o)}function r(e,t){for(var n=[],r=e;r<t;++r)n.push(new Vt(c[r],i(r),l));return n}var s,a=o.from,u=o.to,c=o.text,h=Xe(e,a.line),d=Xe(e,u.line),f=X(c),p=i(c.length-1),g=u.line-a.line;o.full?(e.insert(0,r(0,c.length)),e.remove(c.length,e.size-c.length)):gi(e,o)?(s=r(0,c.length-1),n(d,d.text,p),g&&e.remove(a.line,g),s.length&&e.insert(a.line,s)):h==d?1==c.length?n(h,h.text.slice(0,a.ch)+f+h.text.slice(u.ch),p):((s=r(1,c.length-1)).push(new Vt(f+h.text.slice(u.ch),p,l)),n(h,h.text.slice(0,a.ch)+c[0],i(0)),e.insert(a.line+1,s)):1==c.length?(n(h,h.text.slice(0,a.ch)+c[0]+d.text.slice(u.ch),i(0)),e.remove(a.line+1,g)):(n(h,h.text.slice(0,a.ch)+c[0],i(0)),n(d,f+d.text.slice(u.ch),p),p=r(1,c.length-1),1<g&&e.remove(a.line+1,g-1),e.insert(a.line+1,p)),nn(e,"change",e,o)}function vi(e,s,a){!function e(t,n,r){if(t.linked)for(var i=0;i<t.linked.length;++i){var o,l=t.linked[i];l.doc!=n&&(o=r&&l.sharedHist,a&&!o||(s(l.doc,o),e(l.doc,t,o)))}}(e,null,!0)}function yi(e,t){if(t.cm)throw new Error("This document is already in use.");Zn((e.doc=t).cm=e),fi(e),bi(e),e.options.direction=t.direction,e.options.lineWrapping||Ut(e),e.options.mode=t.modeOption,er(e)}function bi(e){("rtl"==e.doc.direction?O:C)(e.display.lineDiv,"CodeMirror-rtl")}function wi(e){this.done=[],this.undone=[],this.undoDepth=e?e.undoDepth:1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e?e.maxGeneration:1}function xi(e,t){var n={from:rt(t.from),to:ui(t),text:Ye(e,t.from,t.to)};return Ti(e,n,t.from.line,t.to.line+1),vi(e,function(e){return Ti(e,n,t.from.line,t.to.line+1)},!0),n}function Ci(e){for(;e.length;){if(!X(e).ranges)break;e.pop()}}function Si(e,t,n,r){var i=e.history;i.undone.length=0;var o,l,s=+new Date;if((i.lastOp==r||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&i.lastModTime>s-(e.cm?e.cm.options.historyEventDelay:500)||"*"==t.origin.charAt(0)))&&(o=(a=i).lastOp==r?(Ci(a.done),X(a.done)):a.done.length&&!X(a.done).ranges?X(a.done):1<a.done.length&&!a.done[a.done.length-2].ranges?(a.done.pop(),X(a.done)):void 0))l=X(o.changes),0==tt(t.from,t.to)&&0==tt(t.from,l.to)?l.to=ui(t):o.changes.push(xi(e,t));else{var a=X(i.done);for(a&&a.ranges||ki(e.sel,i.done),o={changes:[xi(e,t)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(n),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=s,i.lastOp=i.lastSelOp=r,i.lastOrigin=i.lastSelOrigin=t.origin,l||ye(e,"historyAdded")}function Li(e,t,n,r){var i,o,l,s=e.history,a=r&&r.origin;n==s.lastSelOp||a&&s.lastSelOrigin==a&&(s.lastModTime==s.lastSelTime&&s.lastOrigin==a||(i=e,o=a,l=X(s.done),e=t,"*"==(o=o.charAt(0))||"+"==o&&l.ranges.length==e.ranges.length&&l.somethingSelected()==e.somethingSelected()&&new Date-i.history.lastSelTime<=(i.cm?i.cm.options.historyEventDelay:500)))?s.done[s.done.length-1]=t:ki(t,s.done),s.lastSelTime=+new Date,s.lastSelOrigin=a,s.lastSelOp=n,r&&!1!==r.clearRedo&&Ci(s.undone)}function ki(e,t){var n=X(t);n&&n.ranges&&n.equals(e)||t.push(e)}function Ti(t,n,e,r){var i=n["spans_"+t.id],o=0;t.iter(Math.max(t.first,e),Math.min(t.first+t.size,r),function(e){e.markedSpans&&((i=i||(n["spans_"+t.id]={}))[o]=e.markedSpans),++o})}function Mi(e,t){var n=t["spans_"+e.id];if(!n)return null;for(var r=[],i=0;i<t.text.length;++i)r.push(function(e){if(!e)return null;for(var t,n=0;n<e.length;++n)e[n].marker.explicitlyCleared?t=t||e.slice(0,n):t&&t.push(e[n]);return t?t.length?t:null:e}(n[i]));return r}function Ni(e,t){var n=Mi(e,t),r=kt(e,t);if(!n)return r;if(!r)return n;for(var i=0;i<n.length;++i){var o=n[i],l=r[i];if(o&&l)e:for(var s=0;s<l.length;++s){for(var a=l[s],u=0;u<o.length;++u)if(o[u].marker==a.marker)continue e;o.push(a)}else l&&(n[i]=l)}return n}function Oi(e,t,n){for(var r=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)r.push(n?oi.prototype.deepCopy.call(o):o);else{var l=o.changes,s=[];r.push({changes:s});for(var a=0;a<l.length;++a){var u,c=l[a];if(s.push({from:c.from,to:c.to,text:c.text}),t)for(var h in c)(u=h.match(/^spans_(\d+)$/))&&-1<I(t,Number(u[1]))&&(X(s)[h]=c[h],delete c[h])}}}return r}function Ai(e,t,n,r){if(r){r=e.anchor;return n&&((e=tt(t,r)<0)!=tt(n,r)<0?(r=t,t=n):e!=tt(t,n)<0&&(t=n)),new li(r,t)}return new li(n||t,t)}function Di(e,t,n,r,i){null==i&&(i=e.cm&&(e.cm.display.shift||e.extend)),Ei(e,new oi([Ai(e.sel.primary(),t,n,i)],0),r)}function Wi(e,t,n){for(var r=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)r[o]=Ai(e.sel.ranges[o],t[o],null,i);Ei(e,si(e.cm,r,e.sel.primIndex),n)}function Hi(e,t,n,r){var i=e.sel.ranges.slice(0);i[t]=n,Ei(e,si(e.cm,i,e.sel.primIndex),r)}function Fi(e,t,n,r){Ei(e,ai(t,n),r)}function Pi(e,t,n){var r=e.history.done,i=X(r);i&&i.ranges?Ii(e,r[r.length-1]=t,n):Ei(e,t,n)}function Ei(e,t,n){Ii(e,t,n),Li(e,e.sel,e.cm?e.cm.curOp.id:NaN,n)}function Ii(e,t,n){var r,i;(xe(e,"beforeSelectionChange")||e.cm&&xe(e.cm,"beforeSelectionChange"))&&(r=e,i=n,i={ranges:(o=t).ranges,update:function(e){this.ranges=[];for(var t=0;t<e.length;t++)this.ranges[t]=new li(st(r,e[t].anchor),st(r,e[t].head))},origin:i&&i.origin},ye(r,"beforeSelectionChange",r,i),r.cm&&ye(r.cm,"beforeSelectionChange",r.cm,i),t=i.ranges!=o.ranges?si(r.cm,i.ranges,i.ranges.length-1):o);var o=n&&n.bias||(tt(t.primary().head,e.sel.primary().head)<0?-1:1);Ri(e,Bi(e,t,o,!0)),n&&!1===n.scroll||!e.cm||"nocursor"==e.cm.getOption("readOnly")||br(e.cm)}function Ri(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,we(e.cm)),nn(e,"cursorActivity",e))}function zi(e){Ri(e,Bi(e,e.sel,null,!1))}function Bi(e,t,n,r){for(var i,o=0;o<t.ranges.length;o++){var l=t.ranges[o],s=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],a=Ui(e,l.anchor,s&&s.anchor,n,r),s=Ui(e,l.head,s&&s.head,n,r);!i&&a==l.anchor&&s==l.head||((i=i||t.ranges.slice(0,o))[o]=new li(a,s))}return i?si(e.cm,i,t.primIndex):t}function Gi(e,t,n,r,i){var o=Xe(e,t.line);if(o.markedSpans)for(var l=0;l<o.markedSpans.length;++l){var s=o.markedSpans[l],a=s.marker,u="selectLeft"in a?!a.selectLeft:a.inclusiveLeft,c="selectRight"in a?!a.selectRight:a.inclusiveRight;if((null==s.from||(u?s.from<=t.ch:s.from<t.ch))&&(null==s.to||(c?s.to>=t.ch:s.to>t.ch))){if(i&&(ye(a,"beforeCursorEnter"),a.explicitlyCleared)){if(o.markedSpans){--l;continue}break}if(a.atomic){if(n){var h=a.find(r<0?1:-1),s=void 0;if((h=(r<0?c:u)?Vi(e,h,-r,h&&h.line==t.line?o:null):h)&&h.line==t.line&&(s=tt(h,n))&&(r<0?s<0:0<s))return Gi(e,h,t,r,i)}a=a.find(r<0?-1:1);return(a=(r<0?u:c)?Vi(e,a,r,a.line==t.line?o:null):a)?Gi(e,a,t,r,i):null}}}return t}function Ui(e,t,n,r,i){r=r||1,r=Gi(e,t,n,r,i)||!i&&Gi(e,t,n,r,!0)||Gi(e,t,n,-r,i)||!i&&Gi(e,t,n,-r,!0);return r||(e.cantEdit=!0,et(e.first,0))}function Vi(e,t,n,r){return n<0&&0==t.ch?t.line>e.first?st(e,et(t.line-1)):null:0<n&&t.ch==(r||Xe(e,t.line)).text.length?t.line<e.first+e.size-1?et(t.line+1,0):null:new et(t.line,t.ch+n)}function Ki(e){e.setSelection(et(e.firstLine(),0),et(e.lastLine()),B)}function ji(i,e,t){var o={canceled:!1,from:e.from,to:e.to,text:e.text,origin:e.origin,cancel:function(){return o.canceled=!0}};return t&&(o.update=function(e,t,n,r){e&&(o.from=st(i,e)),t&&(o.to=st(i,t)),n&&(o.text=n),void 0!==r&&(o.origin=r)}),ye(i,"beforeChange",i,o),i.cm&&ye(i.cm,"beforeChange",i.cm,o),o.canceled?(i.cm&&(i.cm.curOp.updateInput=2),null):{from:o.from,to:o.to,text:o.text,origin:o.origin}}function Xi(e,t,n){if(e.cm){if(!e.cm.curOp)return Ir(e.cm,Xi)(e,t,n);if(e.cm.state.suppressEdits)return}if(!(xe(e,"beforeChange")||e.cm&&xe(e.cm,"beforeChange"))||(t=ji(e,t,!0))){var r=xt&&!n&&function(e,t,n){var r=null;if(e.iter(t.line,n.line+1,function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var n=e.markedSpans[t].marker;!n.readOnly||r&&-1!=I(r,n)||(r=r||[]).push(n)}}),!r)return null;for(var i=[{from:t,to:n}],o=0;o<r.length;++o)for(var l=r[o],s=l.find(0),a=0;a<i.length;++a){var u,c,h,d=i[a];tt(d.to,s.from)<0||0<tt(d.from,s.to)||(u=[a,1],c=tt(d.from,s.from),h=tt(d.to,s.to),(c<0||!l.inclusiveLeft&&!c)&&u.push({from:d.from,to:s.from}),(0<h||!l.inclusiveRight&&!h)&&u.push({from:s.to,to:d.to}),i.splice.apply(i,u),a+=u.length-3)}return i}(e,t.from,t.to);if(r)for(var i=r.length-1;0<=i;--i)Yi(e,{from:r[i].from,to:r[i].to,text:i?[""]:t.text,origin:t.origin});else Yi(e,t)}}function Yi(e,n){var t,r;1==n.text.length&&""==n.text[0]&&0==tt(n.from,n.to)||(t=hi(e,n),Si(e,n,t,e.cm?e.cm.curOp.id:NaN),qi(e,n,t,kt(e,n)),r=[],vi(e,function(e,t){t||-1!=I(r,e.history)||(eo(e.history,n),r.push(e.history)),qi(e,n,null,kt(e,n))}))}function $i(i,o,e){var t=i.cm&&i.cm.state.suppressEdits;if(!t||e){for(var l,n=i.history,r=i.sel,s="undo"==o?n.done:n.undone,a="undo"==o?n.undone:n.done,u=0;u<s.length&&(l=s[u],e?!l.ranges||l.equals(i.sel):l.ranges);u++);if(u!=s.length){for(n.lastOrigin=n.lastSelOrigin=null;;){if(!(l=s.pop()).ranges){if(t)return void s.push(l);break}if(ki(l,a),e&&!l.equals(i.sel))return void Ei(i,l,{clearRedo:!1});r=l}var c=[];ki(r,a),a.push({changes:c,generation:n.generation}),n.generation=l.generation||++n.maxGeneration;for(var h=xe(i,"beforeChange")||i.cm&&xe(i.cm,"beforeChange"),d=l.changes.length-1;0<=d;--d){var f=function(e){var n=l.changes[e];if(n.origin=o,h&&!ji(i,n,!1))return s.length=0,{};c.push(xi(i,n));var t=e?hi(i,n):X(s);qi(i,n,t,Ni(i,n)),!e&&i.cm&&i.cm.scrollIntoView({from:n.from,to:ui(n)});var r=[];vi(i,function(e,t){t||-1!=I(r,e.history)||(eo(e.history,n),r.push(e.history)),qi(e,n,null,Ni(e,n))})}(d);if(f)return f.v}}}}function _i(e,t){if(0!=t&&(e.first+=t,e.sel=new oi(Y(e.sel.ranges,function(e){return new li(et(e.anchor.line+t,e.anchor.ch),et(e.head.line+t,e.head.ch))}),e.sel.primIndex),e.cm)){er(e.cm,e.first,e.first-t,t);for(var n=e.cm.display,r=n.viewFrom;r<n.viewTo;r++)tr(e.cm,r,"gutter")}}function qi(e,t,n,r){if(e.cm&&!e.cm.curOp)return Ir(e.cm,qi)(e,t,n,r);var i;t.to.line<e.first?_i(e,t.text.length-1-(t.to.line-t.from.line)):t.from.line>e.lastLine()||(t.from.line<e.first&&(_i(e,i=t.text.length-1-(e.first-t.from.line)),t={from:et(e.first,0),to:et(t.to.line+i,t.to.ch),text:[X(t.text)],origin:t.origin}),i=e.lastLine(),(t=t.to.line>i?{from:t.from,to:et(i,Xe(e,i).text.length),text:[t.text[0]],origin:t.origin}:t).removed=Ye(e,t.from,t.to),n=n||hi(e,t),e.cm?function(e,t,n){var r=e.doc,i=e.display,o=t.from,l=t.to,s=!1,a=o.line;e.options.lineWrapping||(a=qe(Et(Xe(r,o.line))),r.iter(a,l.line+1,function(e){if(e==i.maxLine)return s=!0}));-1<r.sel.contains(t.from,t.to)&&we(e);mi(r,t,n,qn(e)),e.options.lineWrapping||(r.iter(a,o.line+t.text.length,function(e){var t=Gt(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,s=!1)}),s&&(e.curOp.updateMaxLine=!0));(function(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var n=e.first,r=t-1;n<r;r--){var i=Xe(e,r).stateAfter;if(i&&(!(i instanceof ut)||r+i.lookAhead<t)){n=r+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,n)}})(r,o.line),Br(e,400);a=t.text.length-(l.line-o.line)-1;t.full?er(e):o.line!=l.line||1!=t.text.length||gi(e.doc,t)?er(e,o.line,l.line+1,a):tr(e,o.line,"text");r=xe(e,"changes"),a=xe(e,"change");(a||r)&&(t={from:o,to:l,text:t.text,removed:t.removed,origin:t.origin},a&&nn(e,"change",e,t),r&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(t));e.display.selForContextMenu=null}(e.cm,t,r):mi(e,t,r),Ii(e,n,B),e.cantEdit&&Ui(e,et(e.firstLine(),0))&&(e.cantEdit=!1))}function Zi(e,t,n,r,i){var o;tt(r=r||n,n)<0&&(n=(o=[r,n])[0],r=o[1]),"string"==typeof t&&(t=e.splitLines(t)),Xi(e,{from:n,to:r,text:t,origin:i})}function Qi(e,t,n,r){n<e.line?e.line+=r:t<e.line&&(e.line=t,e.ch=0)}function Ji(e,t,n,r){for(var i=0;i<e.length;++i){var o=e[i],l=!0;if(o.ranges){o.copied||((o=e[i]=o.deepCopy()).copied=!0);for(var s=0;s<o.ranges.length;s++)Qi(o.ranges[s].anchor,t,n,r),Qi(o.ranges[s].head,t,n,r)}else{for(var a=0;a<o.changes.length;++a){var u=o.changes[a];if(n<u.from.line)u.from=et(u.from.line+r,u.from.ch),u.to=et(u.to.line+r,u.to.ch);else if(t<=u.to.line){l=!1;break}}l||(e.splice(0,i+1),i=0)}}}function eo(e,t){var n=t.from.line,r=t.to.line,t=t.text.length-(r-n)-1;Ji(e.done,n,r,t),Ji(e.undone,n,r,t)}function to(e,t,n,r){var i=t,o=t;return"number"==typeof t?o=Xe(e,lt(e,t)):i=qe(t),null==i?null:(r(o,i)&&e.cm&&tr(e.cm,i,n),o)}function no(e){this.lines=e,this.parent=null;for(var t=0,n=0;n<e.length;++n)e[n].parent=this,t+=e[n].height;this.height=t}function ro(e){this.children=e;for(var t=0,n=0,r=0;r<e.length;++r){var i=e[r];t+=i.chunkSize(),n+=i.height,i.parent=this}this.size=t,this.height=n,this.parent=null}li.prototype.from=function(){return ot(this.anchor,this.head)},li.prototype.to=function(){return it(this.anchor,this.head)},li.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},no.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var n,r=e,i=e+t;r<i;++r){var o=this.lines[r];this.height-=o.height,(n=o).parent=null,Mt(n),nn(o,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,n){this.height+=n,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var r=0;r<t.length;++r)t[r].parent=this},iterN:function(e,t,n){for(var r=e+t;e<r;++e)if(n(this.lines[e]))return!0}},ro.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var n,r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<o){var l=Math.min(t,o-e),s=i.height;if(i.removeInner(e,l),this.height-=s-i.height,o==l&&(this.children.splice(r--,1),i.parent=null),0==(t-=l))break;e=0}else e-=o}this.size-t<25&&(1<this.children.length||!(this.children[0]instanceof no))&&(this.collapse(n=[]),this.children=[new no(n)],this.children[0].parent=this)},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,n){this.size+=t.length,this.height+=n;for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<=o){if(i.insertInner(e,t,n),i.lines&&50<i.lines.length){for(var l=i.lines.length%25+25,s=l;s<i.lines.length;){var a=new no(i.lines.slice(s,s+=25));i.height-=a.height,this.children.splice(++r,0,a),a.parent=this}i.lines=i.lines.slice(0,l),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t,n=new ro(e.children.splice(e.children.length-5,5))}while(e.parent?(e.size-=n.size,e.height-=n.height,t=I(e.parent.children,e),e.parent.children.splice(t+1,0,n)):(((t=new ro(e.children)).parent=e).children=[t,n],e=t),n.parent=e.parent,10<e.children.length);e.parent.maybeSpill()}},iterN:function(e,t,n){for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<o){var l=Math.min(t,o-e);if(i.iterN(e,l,n))return!0;if(0==(t-=l))break;e=0}else e-=o}}};function io(e,t,n){if(n)for(var r in n)n.hasOwnProperty(r)&&(this[r]=n[r]);this.doc=e,this.node=t}function oo(e,t,n){Bt(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&yr(e,n)}io.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,n=this.line,r=qe(n);if(null!=r&&t){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(n.widgets=null);var o=dn(this);_e(n,Math.max(0,n.height-o)),e&&(Er(e,function(){oo(e,n,-o),tr(e,r,"widget")}),nn(e,"lineWidgetCleared",e,this,r))}},io.prototype.changed=function(){var e=this,t=this.height,n=this.doc.cm,r=this.line;this.height=null;var i=dn(this)-t;i&&(zt(this.doc,r)||_e(r,r.height+i),n&&Er(n,function(){n.curOp.forceUpdate=!0,oo(n,r,i),nn(n,"lineWidgetChanged",n,e,qe(r))}))},Ce(io);function lo(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++so}var so=0;function ao(r,i,o,e,t){if(e&&e.shared)return function(e,n,r,i,o){(i=F(i)).shared=!1;var l=[ao(e,n,r,i,o)],s=l[0],a=i.widgetNode;return vi(e,function(e){a&&(i.widgetNode=a.cloneNode(!0)),l.push(ao(e,st(e,n),st(e,r),i,o));for(var t=0;t<e.linked.length;++t)if(e.linked[t].isParent)return;s=X(l)}),new uo(l,s)}(r,i,o,e,t);if(r.cm&&!r.cm.curOp)return Ir(r.cm,ao)(r,i,o,e,t);var l=new lo(r,t),t=tt(i,o);if(e&&F(e,l,!1),0<t||0==t&&!1!==l.clearWhenEmpty)return l;if(l.replacedWith&&(l.collapsed=!0,l.widgetNode=k("span",[l.replacedWith],"CodeMirror-widget"),e.handleMouseEvents||l.widgetNode.setAttribute("cm-ignore-events","true"),e.insertLeft&&(l.widgetNode.insertLeft=!0)),l.collapsed){if(Pt(r,i.line,i,o,l)||i.line!=o.line&&Pt(r,o.line,i,o,l))throw new Error("Inserting collapsed marker partially overlapping an existing one");Ct=!0}l.addToHistory&&Si(r,{from:i,to:o,origin:"markText"},r.sel,NaN);var s,a=i.line,u=r.cm;if(r.iter(a,o.line+1,function(e){var t,n;u&&l.collapsed&&!u.options.lineWrapping&&Et(e)==u.display.maxLine&&(s=!0),l.collapsed&&a!=i.line&&_e(e,0),t=e,n=new St(l,a==i.line?i.ch:null,a==o.line?o.ch:null),(e=(e=r.cm&&r.cm.curOp)&&window.WeakSet&&(e.markedSpans||(e.markedSpans=new WeakSet)))&&e.has(t.markedSpans)?t.markedSpans.push(n):(t.markedSpans=t.markedSpans?t.markedSpans.concat([n]):[n],e&&e.add(t.markedSpans)),n.marker.attachLine(t),++a}),l.collapsed&&r.iter(i.line,o.line+1,function(e){zt(r,e)&&_e(e,0)}),l.clearOnEnter&&ge(l,"beforeCursorEnter",function(){return l.clear()}),l.readOnly&&(xt=!0,(r.history.done.length||r.history.undone.length)&&r.clearHistory()),l.collapsed&&(l.id=++so,l.atomic=!0),u){if(s&&(u.curOp.updateMaxLine=!0),l.collapsed)er(u,i.line,o.line+1);else if(l.className||l.startStyle||l.endStyle||l.css||l.attributes||l.title)for(var n=i.line;n<=o.line;n++)tr(u,n,"text");l.atomic&&zi(u.doc),nn(u,"markerAdded",u,l)}return l}lo.prototype.clear=function(){if(!this.explicitlyCleared){var e,t=this.doc.cm,n=t&&!t.curOp;n&&Fr(t),!xe(this,"clear")||(e=this.find())&&nn(this,"clear",e.from,e.to);for(var r=null,i=null,o=0;o<this.lines.length;++o){var l=this.lines[o],s=Lt(l.markedSpans,this);t&&!this.collapsed?tr(t,qe(l),"text"):t&&(null!=s.to&&(i=qe(l)),null!=s.from&&(r=qe(l))),l.markedSpans=function(e,t){for(var n,r=0;r<e.length;++r)e[r]!=t&&(n=n||[]).push(e[r]);return n}(l.markedSpans,s),null==s.from&&this.collapsed&&!zt(this.doc,l)&&t&&_e(l,Xn(t.display))}if(t&&this.collapsed&&!t.options.lineWrapping)for(var a=0;a<this.lines.length;++a){var u=Et(this.lines[a]),c=Gt(u);c>t.display.maxLineLength&&(t.display.maxLine=u,t.display.maxLineLength=c,t.display.maxLineChanged=!0)}null!=r&&t&&this.collapsed&&er(t,r,i+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,t&&zi(t.doc)),t&&nn(t,"markerCleared",t,this,r,i),n&&Pr(t),this.parent&&this.parent.clear()}},lo.prototype.find=function(e,t){var n,r;null==e&&"bookmark"==this.type&&(e=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],l=Lt(o.markedSpans,this);if(null!=l.from&&(n=et(t?o:qe(o),l.from),-1==e))return n;if(null!=l.to&&(r=et(t?o:qe(o),l.to),1==e))return r}return n&&{from:n,to:r}},lo.prototype.changed=function(){var n=this,r=this.find(-1,!0),i=this,o=this.doc.cm;r&&o&&Er(o,function(){var e=r.line,t=qe(r.line),t=Cn(o,t);t&&(Nn(t),o.curOp.selectionChanged=o.curOp.forceUpdate=!0),o.curOp.updateMaxLine=!0,zt(i.doc,e)||null==i.height||(t=i.height,i.height=null,(t=dn(i)-t)&&_e(e,e.height+t)),nn(o,"markerChanged",o,n)})},lo.prototype.attachLine=function(e){var t;!this.lines.length&&this.doc.cm&&((t=this.doc.cm.curOp).maybeHiddenMarkers&&-1!=I(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)),this.lines.push(e)},lo.prototype.detachLine=function(e){this.lines.splice(I(this.lines,e),1),!this.lines.length&&this.doc.cm&&((e=this.doc.cm.curOp).maybeHiddenMarkers||(e.maybeHiddenMarkers=[])).push(this)},Ce(lo);var uo=function(e,t){this.markers=e,this.primary=t;for(var n=0;n<e.length;++n)e[n].parent=this};function co(e){return e.findMarks(et(e.first,0),e.clipPos(et(e.lastLine())),function(e){return e.parent})}function ho(o){for(var e=0;e<o.length;e++)!function(e){var t=o[e],n=[t.primary.doc];vi(t.primary.doc,function(e){return n.push(e)});for(var r=0;r<t.markers.length;r++){var i=t.markers[r];-1==I(n,i.doc)&&(i.parent=null,t.markers.splice(r--,1))}}(e)}uo.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();nn(this,"clear")}},uo.prototype.find=function(e,t){return this.primary.find(e,t)},Ce(uo);function fo(e,t,n,r,i){if(!(this instanceof fo))return new fo(e,t,n,r,i);null==n&&(n=0),ro.call(this,[new no([new Vt("",null)])]),this.first=n,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,n=et(this.modeFrontier=this.highlightFrontier=n,0),this.sel=ai(n),this.history=new wi(null),this.id=++po,this.modeOption=t,this.lineSep=r,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),mi(this,{from:n,to:n,text:e}),Ei(this,ai(n),B)}var po=0;(fo.prototype=_(ro.prototype,{constructor:fo,iter:function(e,t,n){n?this.iterN(e-this.first,t-e,n):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var n=0,r=0;r<t.length;++r)n+=t[r].height;this.insertInner(e-this.first,t,n)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=$e(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:zr(function(e){var t=et(this.first,0),n=this.first+this.size-1;Xi(this,{from:t,to:et(n,Xe(this,n).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&wr(this.cm,0,0),Ei(this,ai(t),B)}),replaceRange:function(e,t,n,r){Zi(this,e,t=st(this,t),n=n?st(this,n):t,r)},getRange:function(e,t,n){t=Ye(this,st(this,e),st(this,t));return!1===n?t:""===n?t.join(""):t.join(n||this.lineSeparator())},getLine:function(e){e=this.getLineHandle(e);return e&&e.text},getLineHandle:function(e){if(Qe(this,e))return Xe(this,e)},getLineNumber:qe,getLineHandleVisualStart:function(e){return Et(e="number"==typeof e?Xe(this,e):e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return st(this,e)},getCursor:function(e){var t=this.sel.primary(),t=null==e||"head"==e?t.head:"anchor"==e?t.anchor:"end"==e||"to"==e||!1===e?t.to():t.from();return t},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:zr(function(e,t,n){Fi(this,st(this,"number"==typeof e?et(e,t||0):e),null,n)}),setSelection:zr(function(e,t,n){Fi(this,st(this,e),st(this,t||e),n)}),extendSelection:zr(function(e,t,n){Di(this,st(this,e),t&&st(this,t),n)}),extendSelections:zr(function(e,t){Wi(this,at(this,e),t)}),extendSelectionsBy:zr(function(e,t){Wi(this,at(this,Y(this.sel.ranges,e)),t)}),setSelections:zr(function(e,t,n){if(e.length){for(var r=[],i=0;i<e.length;i++)r[i]=new li(st(this,e[i].anchor),st(this,e[i].head||e[i].anchor));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),Ei(this,si(this.cm,r,t),n)}}),addSelection:zr(function(e,t,n){var r=this.sel.ranges.slice(0);r.push(new li(st(this,e),st(this,t||e))),Ei(this,si(this.cm,r,r.length-1),n)}),getSelection:function(e){for(var t=this.sel.ranges,n=0;n<t.length;n++)var r=Ye(this,t[n].from(),t[n].to()),i=i?i.concat(r):r;return!1===e?i:i.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],n=this.sel.ranges,r=0;r<n.length;r++){var i=Ye(this,n[r].from(),n[r].to());!1!==e&&(i=i.join(e||this.lineSeparator())),t[r]=i}return t},replaceSelection:function(e,t,n){for(var r=[],i=0;i<this.sel.ranges.length;i++)r[i]=e;this.replaceSelections(r,t,n||"+input")},replaceSelections:zr(function(e,t,n){for(var r=[],i=this.sel,o=0;o<i.ranges.length;o++){var l=i.ranges[o];r[o]={from:l.from(),to:l.to(),text:this.splitLines(e[o]),origin:n}}for(var t=t&&"end"!=t&&function(e,t,n){for(var r=[],i=u=et(e.first,0),o=0;o<t.length;o++){var l=t[o],s=di(l.from,u,i),a=di(ui(l),u,i),u=l.to,i=a;"around"==n?(l=tt((l=e.sel.ranges[o]).head,l.anchor)<0,r[o]=new li(l?a:s,l?s:a)):r[o]=new li(s,s)}return new oi(r,e.sel.primIndex)}(this,r,t),s=r.length-1;0<=s;s--)Xi(this,r[s]);t?Pi(this,t):this.cm&&br(this.cm)}),undo:zr(function(){$i(this,"undo")}),redo:zr(function(){$i(this,"redo")}),undoSelection:zr(function(){$i(this,"undo",!0)}),redoSelection:zr(function(){$i(this,"redo",!0)}),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,n=0,r=0;r<e.done.length;r++)e.done[r].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++n;return{undo:t,redo:n}},clearHistory:function(){var t=this;this.history=new wi(this.history),vi(this,function(e){return e.history=t.history},!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:Oi(this.history.done),undone:Oi(this.history.undone)}},setHistory:function(e){var t=this.history=new wi(this.history);t.done=Oi(e.done.slice(0),null,!0),t.undone=Oi(e.undone.slice(0),null,!0)},setGutterMarker:zr(function(e,n,r){return to(this,e,"gutter",function(e){var t=e.gutterMarkers||(e.gutterMarkers={});return!(t[n]=r)&&J(t)&&(e.gutterMarkers=null),1})}),clearGutter:zr(function(t){var n=this;this.iter(function(e){e.gutterMarkers&&e.gutterMarkers[t]&&to(n,e,"gutter",function(){return e.gutterMarkers[t]=null,J(e.gutterMarkers)&&(e.gutterMarkers=null),1})})}),lineInfo:function(e){var t;if("number"==typeof e){if(!Qe(this,e))return null;if(!(e=Xe(this,t=e)))return null}else if(null==(t=qe(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:zr(function(e,n,r){return to(this,e,"gutter"==n?"gutter":"class",function(e){var t="text"==n?"textClass":"background"==n?"bgClass":"gutter"==n?"gutterClass":"wrapClass";if(e[t]){if(x(r).test(e[t]))return;e[t]+=" "+r}else e[t]=r;return 1})}),removeLineClass:zr(function(e,o,l){return to(this,e,"gutter"==o?"gutter":"class",function(e){var t="text"==o?"textClass":"background"==o?"bgClass":"gutter"==o?"gutterClass":"wrapClass",n=e[t];if(n){if(null==l)e[t]=null;else{var r=n.match(x(l));if(!r)return;var i=r.index+r[0].length;e[t]=n.slice(0,r.index)+(r.index&&i!=n.length?" ":"")+n.slice(i)||null}return 1}})}),addLineWidget:zr(function(e,t,n){return e=e,i=new io(r=this,t,n),(o=r.cm)&&i.noHScroll&&(o.display.alignWidgets=!0),to(r,e,"widget",function(e){var t=e.widgets||(e.widgets=[]);return null==i.insertAt?t.push(i):t.splice(Math.min(t.length,Math.max(0,i.insertAt)),0,i),i.line=e,o&&!zt(r,e)&&(t=Bt(e)<r.scrollTop,_e(e,e.height+dn(i)),t&&yr(o,i.height),o.curOp.forceUpdate=!0),1}),o&&nn(o,"lineWidgetAdded",o,i,"number"==typeof e?e:qe(e)),i;var r,i,o}),removeLineWidget:function(e){e.clear()},markText:function(e,t,n){return ao(this,st(this,e),st(this,t),n,n&&n.type||"range")},setBookmark:function(e,t){t={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return ao(this,e=st(this,e),e,t,"bookmark")},findMarksAt:function(e){var t=[],n=Xe(this,(e=st(this,e)).line).markedSpans;if(n)for(var r=0;r<n.length;++r){var i=n[r];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(i,o,l){i=st(this,i),o=st(this,o);var s=[],a=i.line;return this.iter(i.line,o.line+1,function(e){var t=e.markedSpans;if(t)for(var n=0;n<t.length;n++){var r=t[n];null!=r.to&&a==i.line&&i.ch>=r.to||null==r.from&&a!=i.line||null!=r.from&&a==o.line&&r.from>=o.ch||l&&!l(r.marker)||s.push(r.marker.parent||r.marker)}++a}),s},getAllMarks:function(){var r=[];return this.iter(function(e){var t=e.markedSpans;if(t)for(var n=0;n<t.length;++n)null!=t[n].from&&r.push(t[n].marker)}),r},posFromIndex:function(t){var n,r=this.first,i=this.lineSeparator().length;return this.iter(function(e){e=e.text.length+i;if(t<e)return n=t,!0;t-=e,++r}),st(this,et(r,n))},indexFromPos:function(e){var t=(e=st(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var n=this.lineSeparator().length;return this.iter(this.first,e.line,function(e){t+=e.text.length+n}),t},copy:function(e){var t=new fo($e(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){var t=this.first,n=this.first+this.size;null!=(e=e||{}).from&&e.from>t&&(t=e.from),null!=e.to&&e.to<n&&(n=e.to);t=new fo($e(this,t,n),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(t.history=this.history),(this.linked||(this.linked=[])).push({doc:t,sharedHist:e.sharedHist}),t.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],function(e,t){for(var n=0;n<t.length;n++){var r=t[n],i=r.find(),o=e.clipPos(i.from),i=e.clipPos(i.to);tt(o,i)&&(i=ao(e,o,i,r.primary,r.primary.type),r.markers.push(i),i.parent=r)}}(t,co(this)),t},unlinkDoc:function(e){if(e instanceof hl&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t)if(this.linked[t].doc==e){this.linked.splice(t,1),e.unlinkDoc(this),ho(co(this));break}var n;e.history==this.history&&(n=[e.id],vi(e,function(e){return n.push(e.id)},!0),e.history=new wi(null),e.history.done=Oi(this.history.done,n),e.history.undone=Oi(this.history.undone,n))},iterLinkedDocs:function(e){vi(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):We(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:zr(function(e){var t;(e="rtl"!=e?"ltr":e)!=this.direction&&(this.direction=e,this.iter(function(e){return e.order=null}),this.cm&&Er(t=this.cm,function(){bi(t),er(t)}))})})).eachLine=fo.prototype.iter;var go=0;function mo(e){var r=this;if(vo(r),!be(r,e)&&!fn(r.display,e)){Se(e),w&&(go=+new Date);var t=Qn(r,e,!0),n=e.dataTransfer.files;if(t&&!r.isReadOnly())if(n&&n.length&&window.FileReader&&window.File)for(var i=n.length,o=Array(i),l=0,s=function(){++l==i&&Ir(r,function(){var e={from:t=st(r.doc,t),to:t,text:r.doc.splitLines(o.filter(function(e){return null!=e}).join(r.doc.lineSeparator())),origin:"paste"};Xi(r.doc,e),Pi(r.doc,ai(st(r.doc,t),st(r.doc,ui(e))))})()},a=0;a<n.length;a++)!function(e,t){var n;r.options.allowDropFileTypes&&-1==I(r.options.allowDropFileTypes,e.type)?s():((n=new FileReader).onerror=function(){return s()},n.onload=function(){var e=n.result;/[\x00-\x08\x0e-\x1f]{2}/.test(e)||(o[t]=e),s()},n.readAsText(e))}(n[a],a);else{if(r.state.draggingText&&-1<r.doc.sel.contains(t))return r.state.draggingText(e),void setTimeout(function(){return r.display.input.focus()},20);try{var u,c=e.dataTransfer.getData("Text");if(c){if(r.state.draggingText&&!r.state.draggingText.copy&&(u=r.listSelections()),Ii(r.doc,ai(t,t)),u)for(var h=0;h<u.length;++h)Zi(r.doc,"",u[h].anchor,u[h].head,"drag");r.replaceSelection(c,"around","paste"),r.display.input.focus()}}catch(e){}}}}function vo(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function yo(t){if(document.getElementsByClassName){for(var e=document.getElementsByClassName("CodeMirror"),n=[],r=0;r<e.length;r++){var i=e[r].CodeMirror;i&&n.push(i)}n.length&&n[0].operation(function(){for(var e=0;e<n.length;e++)t(n[e])})}}var bo=!1;function wo(){var e;bo||(ge(window,"resize",function(){null==e&&(e=setTimeout(function(){e=null,yo(xo)},100))}),ge(window,"blur",function(){return yo(fr)}),bo=!0)}function xo(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var Co={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Mod",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},So=0;So<10;So++)Co[So+48]=Co[So+96]=String(So);for(var Lo=65;Lo<=90;Lo++)Co[Lo]=String.fromCharCode(Lo);for(var ko=1;ko<=12;ko++)Co[ko+111]=Co[ko+63235]="F"+ko;var To={};function Mo(e){var t,n,r,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var l=0;l<o.length-1;l++){var s=o[l];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else{if(!/^s(hift)?$/i.test(s))throw new Error("Unrecognized modifier name: "+s);r=!0}}return t&&(e="Alt-"+e),n&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),e=r?"Shift-"+e:e}function No(e){var t,n,r={};for(t in e)if(e.hasOwnProperty(t)){var i=e[t];if(!/^(name|fallthrough|(de|at)tach)$/.test(t))if("..."!=i){for(var o=Y(t.split(" "),Mo),l=0;l<o.length;l++){var s=void 0,a=void 0,s=l==o.length-1?(a=o.join(" "),i):(a=o.slice(0,l+1).join(" "),"..."),u=r[a];if(u){if(u!=s)throw new Error("Inconsistent bindings for "+a)}else r[a]=s}delete e[t]}else delete e[t]}for(n in r)e[n]=r[n];return e}function Oo(e,t,n,r){var i=(t=Ho(t)).call?t.call(e,r):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&n(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return Oo(e,t.fallthrough,n,r);for(var o=0;o<t.fallthrough.length;o++){var l=Oo(e,t.fallthrough[o],n,r);if(l)return l}}}function Ao(e){e="string"==typeof e?e:Co[e.keyCode];return"Ctrl"==e||"Alt"==e||"Shift"==e||"Mod"==e}function Do(e,t,n){var r=e;return t.altKey&&"Alt"!=r&&(e="Alt-"+e),(y?t.metaKey:t.ctrlKey)&&"Ctrl"!=r&&(e="Ctrl-"+e),(y?t.ctrlKey:t.metaKey)&&"Mod"!=r&&(e="Cmd-"+e),e=!n&&t.shiftKey&&"Shift"!=r?"Shift-"+e:e}function Wo(e,t){if(p&&34==e.keyCode&&e.char)return!1;var n=Co[e.keyCode];return null!=n&&!e.altGraphKey&&Do(n=3==e.keyCode&&e.code?e.code:n,e,t)}function Ho(e){return"string"==typeof e?To[e]:e}function Fo(t,e){for(var n=t.doc.sel.ranges,r=[],i=0;i<n.length;i++){for(var o=e(n[i]);r.length&&tt(o.from,X(r).to)<=0;){var l=r.pop();if(tt(l.from,o.from)<0){o.from=l.from;break}}r.push(o)}Er(t,function(){for(var e=r.length-1;0<=e;e--)Zi(t.doc,"",r[e].from,r[e].to,"+delete");br(t)})}function Po(e,t,n){n=ne(e.text,t+n,n);return n<0||n>e.text.length?null:n}function Eo(e,t,n){e=Po(e,t.ch,n);return null==e?null:new et(t.line,e,n<0?"after":"before")}function Io(e,t,n,r,i){if(e){"rtl"==t.doc.direction&&(i=-i);var o=fe(n,t.doc.direction);if(o){var l,s,a,e=i<0?X(o):o[0],o=i<0==(1==e.level)?"after":"before";return 0<e.level||"rtl"==t.doc.direction?(l=Sn(t,n),s=i<0?n.text.length-1:0,a=Ln(t,l,s).top,s=re(function(e){return Ln(t,l,e).top==a},i<0==(1==e.level)?e.from:e.to-1,s),"before"==o&&(s=Po(n,s,1))):s=i<0?e.to:e.from,new et(r,s,o)}}return new et(r,i<0?n.text.length:0,i<0?"before":"after")}function Ro(t,n,s,e){var a=fe(n,t.doc.direction);if(!a)return Eo(n,s,e);s.ch>=n.text.length?(s.ch=n.text.length,s.sticky="before"):s.ch<=0&&(s.ch=0,s.sticky="after");var r=oe(a,s.ch,s.sticky),i=a[r];if("ltr"==t.doc.direction&&i.level%2==0&&(0<e?i.to>s.ch:i.from<s.ch))return Eo(n,s,e);function u(e,t){return Po(n,e instanceof et?e.ch:e,t)}function o(e){return t.options.lineWrapping?(l=l||Sn(t,n),Un(t,n,l,e)):{begin:0,end:n.text.length}}var l,c=o("before"==s.sticky?u(s,-1):s.ch);if("rtl"==t.doc.direction||1==i.level){var h=1==i.level==e<0,d=u(s,h?1:-1);if(null!=d&&(h?d<=i.to&&d<=c.end:d>=i.from&&d>=c.begin))return new et(s.line,d,h?"before":"after")}function f(e,t,n){for(var r=function(e,t){return t?new et(s.line,u(e,1),"before"):new et(s.line,e,"after")};0<=e&&e<a.length;e+=t){var i=a[e],o=0<t==(1!=i.level),l=o?n.begin:u(n.end,-1);if(i.from<=l&&l<i.to)return r(l,o);if(l=o?i.from:u(i.to,-1),n.begin<=l&&l<n.end)return r(l,o)}}r=f(r+e,e,c);if(r)return r;c=0<e?c.end:u(c.begin,-1);return null==c||0<e&&c==n.text.length||!(r=f(0<e?0:a.length-1,e,o(c)))?null:r}To.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},To.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},To.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},To.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},To.default=g?To.macDefault:To.pcDefault;var zo={selectAll:Ki,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),B)},killLine:function(n){return Fo(n,function(e){if(e.empty()){var t=Xe(n.doc,e.head.line).text.length;return e.head.ch==t&&e.head.line<n.lastLine()?{from:e.head,to:et(e.head.line+1,0)}:{from:e.head,to:et(e.head.line,t)}}return{from:e.from(),to:e.to()}})},deleteLine:function(t){return Fo(t,function(e){return{from:et(e.from().line,0),to:st(t.doc,et(e.to().line+1,0))}})},delLineLeft:function(e){return Fo(e,function(e){return{from:et(e.from().line,0),to:e.from()}})},delWrappedLineLeft:function(n){return Fo(n,function(e){var t=n.charCoords(e.head,"div").top+5;return{from:n.coordsChar({left:0,top:t},"div"),to:e.from()}})},delWrappedLineRight:function(n){return Fo(n,function(e){var t=n.charCoords(e.head,"div").top+5,t=n.coordsChar({left:n.display.lineDiv.offsetWidth+100,top:t},"div");return{from:e.from(),to:t}})},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(et(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(et(e.lastLine()))},goLineStart:function(t){return t.extendSelectionsBy(function(e){return Bo(t,e.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(t){return t.extendSelectionsBy(function(e){return Go(t,e.head)},{origin:"+move",bias:1})},goLineEnd:function(t){return t.extendSelectionsBy(function(e){return function(e,t){var n=Xe(e.doc,t),r=function(e){for(var t;t=Ft(e);)e=t.find(1,!0).line;return e}(n);r!=n&&(t=qe(r));return Io(!0,e,n,t,-1)}(t,e.head.line)},{origin:"+move",bias:-1})},goLineRight:function(t){return t.extendSelectionsBy(function(e){e=t.cursorCoords(e.head,"div").top+5;return t.coordsChar({left:t.display.lineDiv.offsetWidth+100,top:e},"div")},U)},goLineLeft:function(t){return t.extendSelectionsBy(function(e){e=t.cursorCoords(e.head,"div").top+5;return t.coordsChar({left:0,top:e},"div")},U)},goLineLeftSmart:function(n){return n.extendSelectionsBy(function(e){var t=n.cursorCoords(e.head,"div").top+5,t=n.coordsChar({left:0,top:t},"div");return t.ch<n.getLine(t.line).search(/\S/)?Go(n,e.head):t},U)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"codepoint")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],n=e.listSelections(),r=e.options.tabSize,i=0;i<n.length;i++){var o=n[i].from(),o=P(e.getLine(o.line),o.ch,r);t.push(j(r-o%r))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(l){return Er(l,function(){for(var e,t,n,r=l.listSelections(),i=[],o=0;o<r.length;o++)r[o].empty()&&(e=r[o].head,(t=Xe(l.doc,e.line).text)&&(0<(e=e.ch==t.length?new et(e.line,e.ch-1):e).ch?(e=new et(e.line,e.ch+1),l.replaceRange(t.charAt(e.ch-1)+t.charAt(e.ch-2),et(e.line,e.ch-2),e,"+transpose")):e.line>l.doc.first&&((n=Xe(l.doc,e.line-1).text)&&(e=new et(e.line,1),l.replaceRange(t.charAt(0)+l.doc.lineSeparator()+n.charAt(n.length-1),et(e.line-1,n.length-1),e,"+transpose")))),i.push(new li(e,e)));l.setSelections(i)})},newlineAndIndent:function(r){return Er(r,function(){for(var e=(t=r.listSelections()).length-1;0<=e;e--)r.replaceRange(r.doc.lineSeparator(),t[e].anchor,t[e].head,"+input");for(var t=r.listSelections(),n=0;n<t.length;n++)r.indentLine(t[n].from().line,null,!0);br(r)})},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function Bo(e,t){var n=Xe(e.doc,t),r=Et(n);return Io(!0,e,r,t=r!=n?qe(r):t,1)}function Go(e,t){var n=Bo(e,t.line),r=Xe(e.doc,n.line),e=fe(r,e.doc.direction);if(e&&0!=e[0].level)return n;r=Math.max(n.ch,r.text.search(/\S/)),t=t.line==n.line&&t.ch<=r&&t.ch;return et(n.line,t?0:r,n.sticky)}function Uo(e,t,n){if("string"==typeof t&&!(t=zo[t]))return!1;e.display.input.ensurePolled();var r=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),n&&(e.display.shift=!1),i=t(e)!=z}finally{e.display.shift=r,e.state.suppressEdits=!1}return i}var Vo=new E;function Ko(e,t,n,r){var i=e.state.keySeq;if(i){if(Ao(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:Vo.set(50,function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())}),jo(e,i+" "+t,n,r))return!0}return jo(e,t,n,r)}function jo(e,t,n,r){r=function(e,t,n){for(var r=0;r<e.state.keyMaps.length;r++){var i=Oo(t,e.state.keyMaps[r],n,e);if(i)return i}return e.options.extraKeys&&Oo(t,e.options.extraKeys,n,e)||Oo(t,e.options.keyMap,n,e)}(e,t,r);return"multi"==r&&(e.state.keySeq=t),"handled"==r&&nn(e,"keyHandled",e,t,n),"handled"!=r&&"multi"!=r||(Se(n),ur(e)),!!r}function Xo(t,e){var n=Wo(e,!0);return!!n&&(e.shiftKey&&!t.state.keySeq?Ko(t,"Shift-"+n,e,function(e){return Uo(t,e,!0)})||Ko(t,n,e,function(e){if("string"==typeof e?/^go[A-Z]/.test(e):e.motion)return Uo(t,e)}):Ko(t,n,e,function(e){return Uo(t,e)}))}var Yo=null;function $o(e){var t,n,r,i=this;function o(e){18!=e.keyCode&&e.altKey||(C(r,"CodeMirror-crosshair"),ve(document,"keyup",o),ve(document,"mouseover",o))}e.target&&e.target!=i.display.input.getField()||(i.curOp.focus=N(),be(i,e)||(w&&v<11&&27==e.keyCode&&(e.returnValue=!1),t=e.keyCode,i.display.shift=16==t||e.shiftKey,n=Xo(i,e),p&&(Yo=n?t:null,!n&&88==t&&!Fe&&(g?e.metaKey:e.ctrlKey)&&i.replaceSelection("",null,"cut")),d&&!g&&!n&&46==t&&e.shiftKey&&!e.ctrlKey&&document.execCommand&&document.execCommand("cut"),18!=t||/\bCodeMirror-crosshair\b/.test(i.display.lineDiv.className)||(O(r=i.display.lineDiv,"CodeMirror-crosshair"),ge(document,"keyup",o),ge(document,"mouseover",o))))}function _o(e){16==e.keyCode&&(this.doc.sel.shift=!1),be(this,e)}function qo(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField()||fn(t.display,e)||be(t,e)||e.ctrlKey&&!e.altKey||g&&e.metaKey)){var n,r=e.keyCode,i=e.charCode;if(p&&r==Yo)return Yo=null,void Se(e);p&&(!e.which||e.which<10)&&Xo(t,e)||"\b"!=(i=String.fromCharCode(null==i?r:i))&&(Ko(n=t,"'"+i+"'",e,function(e){return Uo(n,e,!0)})||t.display.input.onKeyPress(e))}}function Zo(e,t,n){this.time=e,this.pos=t,this.button=n}var Qo,Jo;function el(e){var t,n,r,i,o,l=this,s=l.display;be(l,e)||s.activeTouch&&s.input.supportsTouch()||(s.input.ensurePolled(),s.shift=e.shiftKey,fn(s,e)?f||(s.scroller.draggable=!1,setTimeout(function(){return s.scroller.draggable=!0},100)):rl(l,e)||(t=Qn(l,e),n=Ne(e),i=t?(r=t,i=n,o=+new Date,Jo&&Jo.compare(o,r,i)?(Qo=Jo=null,"triple"):Qo&&Qo.compare(o,r,i)?(Jo=new Zo(o,r,i),Qo=null,"double"):(Qo=new Zo(o,r,i),Jo=null,"single")):"single",window.focus(),1==n&&l.state.selectingText&&l.state.selectingText(e),t&&function(n,e,r,t,i){var o="Click";"double"==t?o="Double"+o:"triple"==t&&(o="Triple"+o);return Ko(n,Do(o=(1==e?"Left":2==e?"Middle":"Right")+o,i),i,function(e){if(!(e="string"==typeof e?zo[e]:e))return!1;var t=!1;try{n.isReadOnly()&&(n.state.suppressEdits=!0),t=e(n,r)!=z}finally{n.state.suppressEdits=!1}return t})}(l,n,t,i,e)||(1==n?t?function(e,t,n,r){w?setTimeout(H(cr,e),0):e.curOp.focus=N();var i,o=function(e,t,n){var r=e.getOption("configureMouse"),i=r?r(e,t,n):{};null==i.unit&&(r=m?n.shiftKey&&n.metaKey:n.altKey,i.unit=r?"rectangle":"single"==t?"char":"double"==t?"word":"line");null!=i.extend&&!e.doc.extend||(i.extend=e.doc.extend||n.shiftKey);null==i.addNew&&(i.addNew=g?n.metaKey:n.ctrlKey);null==i.moveOnDrag&&(i.moveOnDrag=!(g?n.altKey:n.ctrlKey));return i}(e,n,r),l=e.doc.sel;(e.options.dragDrop&&De&&!e.isReadOnly()&&"single"==n&&-1<(i=l.contains(t))&&(tt((i=l.ranges[i]).from(),t)<0||0<t.xRel)&&(0<tt(i.to(),t)||t.xRel<0)?function(t,n,r,i){var o=t.display,l=!1,s=Ir(t,function(e){f&&(o.scroller.draggable=!1),t.state.draggingText=!1,t.state.delayingBlurEvent&&(t.hasFocus()?t.state.delayingBlurEvent=!1:hr(t)),ve(o.wrapper.ownerDocument,"mouseup",s),ve(o.wrapper.ownerDocument,"mousemove",a),ve(o.scroller,"dragstart",u),ve(o.scroller,"drop",s),l||(Se(e),i.addNew||Di(t.doc,r,null,null,i.extend),f&&!c||w&&9==v?setTimeout(function(){o.wrapper.ownerDocument.body.focus({preventScroll:!0}),o.input.focus()},20):o.input.focus())}),a=function(e){l=l||10<=Math.abs(n.clientX-e.clientX)+Math.abs(n.clientY-e.clientY)},u=function(){return l=!0};f&&(o.scroller.draggable=!0);(t.state.draggingText=s).copy=!i.moveOnDrag,ge(o.wrapper.ownerDocument,"mouseup",s),ge(o.wrapper.ownerDocument,"mousemove",a),ge(o.scroller,"dragstart",u),ge(o.scroller,"drop",s),t.state.delayingBlurEvent=!0,setTimeout(function(){return o.input.focus()},20),o.scroller.dragDrop&&o.scroller.dragDrop()}:function(d,e,f,p){w&&hr(d);var l=d.display,g=d.doc;Se(e);var m,v,y=g.sel,t=y.ranges;p.addNew&&!p.extend?(v=g.sel.contains(f),m=-1<v?t[v]:new li(f,f)):(m=g.sel.primary(),v=g.sel.primIndex);"rectangle"==p.unit?(p.addNew||(m=new li(f,f)),f=Qn(d,e,!0,!0),v=-1):(e=tl(d,f,p.unit),m=p.extend?Ai(m,e.anchor,e.head,p.extend):e);p.addNew?-1==v?(v=t.length,Ei(g,si(d,t.concat([m]),v),{scroll:!1,origin:"*mouse"})):1<t.length&&t[v].empty()&&"char"==p.unit&&!p.extend?(Ei(g,si(d,t.slice(0,v).concat(t.slice(v+1)),0),{scroll:!1,origin:"*mouse"}),y=g.sel):Hi(g,v,m,G):(Ei(g,new oi([m],v=0),G),y=g.sel);var b=f;function s(e){if(0!=tt(b,e))if(b=e,"rectangle"==p.unit){for(var t=[],n=d.options.tabSize,r=P(Xe(g,f.line).text,f.ch,n),i=P(Xe(g,e.line).text,e.ch,n),o=Math.min(r,i),l=Math.max(r,i),s=Math.min(f.line,e.line),a=Math.min(d.lastLine(),Math.max(f.line,e.line));s<=a;s++){var u=Xe(g,s).text,c=V(u,o,n);o==l?t.push(new li(et(s,c),et(s,c))):u.length>c&&t.push(new li(et(s,c),et(s,V(u,l,n))))}t.length||t.push(new li(f,f)),Ei(g,si(d,y.ranges.slice(0,v).concat(t),v),{origin:"*mouse",scroll:!1}),d.scrollIntoView(e)}else{var h,r=m,i=tl(d,e,p.unit),e=r.anchor,e=0<tt(i.anchor,e)?(h=i.head,ot(r.from(),i.anchor)):(h=i.anchor,it(r.to(),i.head)),i=y.ranges.slice(0);i[v]=function(e,t){var n=t.anchor,r=t.head,i=Xe(e.doc,n.line);if(0==tt(n,r)&&n.sticky==r.sticky)return t;var o=fe(i);if(!o)return t;var l=oe(o,n.ch,n.sticky),s=o[l];if(s.from!=n.ch&&s.to!=n.ch)return t;i=l+(s.from==n.ch==(1!=s.level)?0:1);if(0==i||i==o.length)return t;a=r.line!=n.line?0<(r.line-n.line)*("ltr"==e.doc.direction?1:-1):(e=oe(o,r.ch,r.sticky),a=e-l||(r.ch-n.ch)*(1==s.level?-1:1),e==i-1||e==i?a<0:0<a);var i=o[i+(a?-1:0)],a=a==(1==i.level),i=a?i.from:i.to,a=a?"after":"before";return n.ch==i&&n.sticky==a?t:new li(new et(n.line,i,a),r)}(d,new li(st(g,e),h)),Ei(g,si(d,i,v),G)}}var a=l.wrapper.getBoundingClientRect(),u=0;function n(e){d.state.selectingText=!1,u=1/0,e&&(Se(e),l.input.focus()),ve(l.wrapper.ownerDocument,"mousemove",r),ve(l.wrapper.ownerDocument,"mouseup",i),g.history.lastSelOrigin=null}var r=Ir(d,function(e){(0!==e.buttons&&Ne(e)?function e(t){var n,r,i=++u,o=Qn(d,t,!0,"rectangle"==p.unit);o&&(0!=tt(o,b)?(d.curOp.focus=N(),s(o),n=mr(l,g),(o.line>=n.to||o.line<n.from)&&setTimeout(Ir(d,function(){u==i&&e(t)}),150)):(r=t.clientY<a.top?-20:t.clientY>a.bottom?20:0)&&setTimeout(Ir(d,function(){u==i&&(l.scroller.scrollTop+=r,e(t))}),50))}:n)(e)}),i=Ir(d,n);d.state.selectingText=i,ge(l.wrapper.ownerDocument,"mousemove",r),ge(l.wrapper.ownerDocument,"mouseup",i)})(e,r,t,o)}(l,t,i,e):Me(e)==s.scroller&&Se(e):2==n?(t&&Di(l.doc,t),setTimeout(function(){return s.input.focus()},20)):3==n&&(b?l.display.input.onContextMenu(e):hr(l)))))}function tl(e,t,n){if("char"==n)return new li(t,t);if("word"==n)return e.findWordAt(t);if("line"==n)return new li(et(t.line,0),st(e.doc,et(t.line+1,0)));t=n(e,t);return new li(t.from,t.to)}function nl(e,t,n,r){var i,o;if(t.touches)i=t.touches[0].clientX,o=t.touches[0].clientY;else try{i=t.clientX,o=t.clientY}catch(e){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;r&&Se(t);var l=e.display,r=l.lineDiv.getBoundingClientRect();if(o>r.bottom||!xe(e,n))return ke(t);o-=r.top-l.viewOffset;for(var s=0;s<e.display.gutterSpecs.length;++s){var a=l.gutters.childNodes[s];if(a&&a.getBoundingClientRect().right>=i)return ye(e,n,e,Ze(e.doc,o),e.display.gutterSpecs[s].className,t),ke(t)}}function rl(e,t){return nl(e,t,"gutterClick",!0)}function il(e,t){var n,r;fn(e.display,t)||(r=t,xe(n=e,"gutterContextMenu")&&nl(n,r,"gutterContextMenu",!1))||be(e,t,"contextmenu")||b||e.display.input.onContextMenu(t)}function ol(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),An(e)}Zo.prototype.compare=function(e,t,n){return this.time+400>e&&0==tt(t,this.pos)&&n==this.button};var ll={toString:function(){return"CodeMirror.Init"}},sl={},al={};function ul(e,t,n){!t!=!(n&&n!=ll)&&(n=e.display.dragFunctions,(t=t?ge:ve)(e.display.scroller,"dragstart",n.start),t(e.display.scroller,"dragenter",n.enter),t(e.display.scroller,"dragover",n.over),t(e.display.scroller,"dragleave",n.leave),t(e.display.scroller,"drop",n.drop))}function cl(e){e.options.lineWrapping?(O(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(C(e.display.wrapper,"CodeMirror-wrap"),Ut(e)),Zn(e),er(e),An(e),setTimeout(function(){return Or(e)},100)}function hl(e,t){var n=this;if(!(this instanceof hl))return new hl(e,t);this.options=t=t?F(t):{},F(sl,t,!1);var r=t.value;"string"==typeof r?r=new fo(r,t.mode,null,t.lineSeparator,t.direction):t.mode&&(r.modeOption=t.mode),this.doc=r;var i,o=new hl.inputStyles[t.inputStyle](this),o=this.display=new Jr(e,r,o,t);for(i in ol(o.wrapper.CodeMirror=this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),Wr(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new E,keySeq:null,specialChars:null},t.autofocus&&!h&&o.input.focus(),w&&v<11&&setTimeout(function(){return n.display.input.reset(!0)},20),function(r){var i=r.display;ge(i.scroller,"mousedown",Ir(r,el)),ge(i.scroller,"dblclick",w&&v<11?Ir(r,function(e){var t;be(r,e)||(!(t=Qn(r,e))||rl(r,e)||fn(r.display,e)||(Se(e),t=r.findWordAt(t),Di(r.doc,t.anchor,t.head)))}):function(e){return be(r,e)||Se(e)});ge(i.scroller,"contextmenu",function(e){return il(r,e)}),ge(i.input.getField(),"contextmenu",function(e){i.scroller.contains(e.target)||il(r,e)});var n,o={end:0};function l(){i.activeTouch&&(n=setTimeout(function(){return i.activeTouch=null},1e3),(o=i.activeTouch).end=+new Date)}function s(e,t){if(null==t.left)return 1;var n=t.left-e.left,e=t.top-e.top;return 400<n*n+e*e}ge(i.scroller,"touchstart",function(e){var t;be(r,e)||function(e){if(1==e.touches.length){e=e.touches[0];return e.radiusX<=1&&e.radiusY<=1}}(e)||rl(r,e)||(i.input.ensurePolled(),clearTimeout(n),t=+new Date,i.activeTouch={start:t,moved:!1,prev:t-o.end<=300?o:null},1==e.touches.length&&(i.activeTouch.left=e.touches[0].pageX,i.activeTouch.top=e.touches[0].pageY))}),ge(i.scroller,"touchmove",function(){i.activeTouch&&(i.activeTouch.moved=!0)}),ge(i.scroller,"touchend",function(e){var t,n=i.activeTouch;n&&!fn(i,e)&&null!=n.left&&!n.moved&&new Date-n.start<300&&(t=r.coordsChar(i.activeTouch,"page"),t=!n.prev||s(n,n.prev)?new li(t,t):!n.prev.prev||s(n,n.prev.prev)?r.findWordAt(t):new li(et(t.line,0),st(r.doc,et(t.line+1,0))),r.setSelection(t.anchor,t.head),r.focus(),Se(e)),l()}),ge(i.scroller,"touchcancel",l),ge(i.scroller,"scroll",function(){i.scroller.clientHeight&&(Sr(r,i.scroller.scrollTop),kr(r,i.scroller.scrollLeft,!0),ye(r,"scroll",r))}),ge(i.scroller,"mousewheel",function(e){return ii(r,e)}),ge(i.scroller,"DOMMouseScroll",function(e){return ii(r,e)}),ge(i.wrapper,"scroll",function(){return i.wrapper.scrollTop=i.wrapper.scrollLeft=0}),i.dragFunctions={enter:function(e){be(r,e)||Te(e)},over:function(e){var t,n;be(r,e)||((n=Qn(t=r,n=e))&&(sr(t,n,n=document.createDocumentFragment()),t.display.dragCursor||(t.display.dragCursor=M("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),t.display.lineSpace.insertBefore(t.display.dragCursor,t.display.cursorDiv)),L(t.display.dragCursor,n)),Te(e))},start:function(e){var t,n;t=r,n=e,w&&(!t.state.draggingText||+new Date-go<100)?Te(n):be(t,n)||fn(t.display,n)||(n.dataTransfer.setData("Text",t.getSelection()),n.dataTransfer.effectAllowed="copyMove",n.dataTransfer.setDragImage&&!c&&((e=M("img",null,null,"position: fixed; left: 0; top: 0;")).src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",p&&(e.width=e.height=1,t.display.wrapper.appendChild(e),e._top=e.offsetTop),n.dataTransfer.setDragImage(e,0,0),p&&e.parentNode.removeChild(e)))},drop:Ir(r,mo),leave:function(e){be(r,e)||vo(r)}};var e=i.input.getField();ge(e,"keyup",function(e){return _o.call(r,e)}),ge(e,"keydown",Ir(r,$o)),ge(e,"keypress",Ir(r,qo)),ge(e,"focus",function(e){return dr(r,e)}),ge(e,"blur",function(e){return fr(r,e)})}(this),wo(),Fr(this),this.curOp.forceUpdate=!0,yi(this,r),t.autofocus&&!h||this.hasFocus()?setTimeout(function(){n.hasFocus()&&!n.state.focused&&dr(n)},20):fr(this),al)al.hasOwnProperty(i)&&al[i](this,t[i],ll);_r(this),t.finishInit&&t.finishInit(this);for(var l=0;l<dl.length;++l)dl[l](this);Pr(this),f&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}hl.defaults=sl,hl.optionHandlers=al;var dl=[];function fl(e,t,n,r){var i,o=e.doc;"smart"==(n=null==n?"add":n)&&(o.mode.indent?i=ft(e,t).state:n="prev");var l=e.options.tabSize,s=Xe(o,t),a=P(s.text,null,l);s.stateAfter&&(s.stateAfter=null);var u=s.text.match(/^\s*/)[0];if(r||/\S/.test(s.text)){if("smart"==n&&((c=o.mode.indent(i,s.text.slice(u.length),s.text))==z||150<c)){if(!r)return;n="prev"}}else c=0,n="not";"prev"==n?c=t>o.first?P(Xe(o,t-1).text,null,l):0:"add"==n?c=a+e.options.indentUnit:"subtract"==n?c=a-e.options.indentUnit:"number"==typeof n&&(c=a+n);var c=Math.max(0,c),h="",d=0;if(e.options.indentWithTabs)for(var f=Math.floor(c/l);f;--f)d+=l,h+="\t";if(d<c&&(h+=j(c-d)),h!=u)return Zi(o,h,et(t,0),et(t,u.length),"+input"),!(s.stateAfter=null);for(var p=0;p<o.sel.ranges.length;p++){var g=o.sel.ranges[p];if(g.head.line==t&&g.head.ch<u.length){g=et(t,u.length);Hi(o,p,new li(g,g));break}}}hl.defineInitHook=function(e){return dl.push(e)};var pl=null;function gl(e){pl=e}function ml(e,t,n,r,i){var o=e.doc;e.display.shift=!1,r=r||o.sel;var l=+new Date-200,s="paste"==i||e.state.pasteIncoming>l,a=We(t),u=null;if(s&&1<r.ranges.length)if(pl&&pl.text.join("\n")==t){if(r.ranges.length%pl.text.length==0)for(var u=[],c=0;c<pl.text.length;c++)u.push(o.splitLines(pl.text[c]))}else a.length==r.ranges.length&&e.options.pasteLinesPerSelection&&(u=Y(a,function(e){return[e]}));for(var h=e.curOp.updateInput,d=r.ranges.length-1;0<=d;d--){var f=r.ranges[d],p=f.from(),g=f.to();f.empty()&&(n&&0<n?p=et(p.line,p.ch-n):e.state.overwrite&&!s?g=et(g.line,Math.min(Xe(o,g.line).text.length,g.ch+X(a).length)):s&&pl&&pl.lineWise&&pl.text.join("\n")==a.join("\n")&&(p=g=et(p.line,0)));g={from:p,to:g,text:u?u[d%u.length]:a,origin:i||(s?"paste":e.state.cutIncoming>l?"cut":"+input")};Xi(e.doc,g),nn(e,"inputRead",e,g)}t&&!s&&yl(e,t),br(e),e.curOp.updateInput<2&&(e.curOp.updateInput=h),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function vl(e,t){var n=e.clipboardData&&e.clipboardData.getData("Text");return n&&(e.preventDefault(),t.isReadOnly()||t.options.disableInput||Er(t,function(){return ml(t,n,0,null,"paste")}),1)}function yl(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var n=e.doc.sel,r=n.ranges.length-1;0<=r;r--){var i=n.ranges[r];if(!(100<i.head.ch||r&&n.ranges[r-1].head.line==i.head.line)){var o=e.getModeAt(i.head),l=!1;if(o.electricChars){for(var s=0;s<o.electricChars.length;s++)if(-1<t.indexOf(o.electricChars.charAt(s))){l=fl(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(Xe(e.doc,i.head.line).text.slice(0,i.head.ch))&&(l=fl(e,i.head.line,"smart"));l&&nn(e,"electricInput",e,i.head.line)}}}function bl(e){for(var t=[],n=[],r=0;r<e.doc.sel.ranges.length;r++){var i=e.doc.sel.ranges[r].head.line,i={anchor:et(i,0),head:et(i+1,0)};n.push(i),t.push(e.getRange(i.anchor,i.head))}return{text:t,ranges:n}}function wl(e,t,n,r){e.setAttribute("autocorrect",n?"":"off"),e.setAttribute("autocapitalize",r?"":"off"),e.setAttribute("spellcheck",!!t)}function xl(){var e=M("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; min-height: 1em; outline: none"),t=M("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return f?e.style.width="1000px":e.setAttribute("wrap","off"),s&&(e.style.border="1px solid black"),wl(e),t}function Cl(i,o,l,s,a){var e=o,t=l,u=Xe(i,o.line),c=a&&"rtl"==i.direction?-l:l;function n(e){var t,n,r;if(null==(n="codepoint"==s?(t=u.text.charCodeAt(o.ch+(0<l?0:-1)),isNaN(t)?null:(n=0<l?55296<=t&&t<56320:56320<=t&&t<57343,new et(o.line,Math.max(0,Math.min(u.text.length,o.ch+l*(n?2:1))),-l))):a?Ro(i.cm,u,o,l):Eo(u,o,l))){if(e||(r=o.line+c)<i.first||r>=i.first+i.size||(o=new et(r,o.ch,o.sticky),!(u=Xe(i,r))))return;o=Io(a,i.cm,u,o.line,c)}else o=n;return 1}if("char"==s||"codepoint"==s)n();else if("column"==s)n(!0);else if("word"==s||"group"==s)for(var r=null,h="group"==s,d=i.cm&&i.cm.getHelper(o,"wordChars"),f=!0;!(l<0)||n(!f);f=!1){var p=u.text.charAt(o.ch)||"\n",p=Q(p,d)?"w":h&&"\n"==p?"n":!h||/\s/.test(p)?null:"p";if(!h||f||p||(p="s"),r&&r!=p){l<0&&(l=1,n(),o.sticky="after");break}if(p&&(r=p),0<l&&!n(!f))break}t=Ui(i,o,e,t,!0);return nt(e,t)&&(t.hitSide=!0),t}function Sl(e,t,n,r){var i,o,l,s=e.doc,a=t.left;for("page"==r?(i=Math.min(e.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight),i=Math.max(i-.5*Xn(e.display),3),o=(0<n?t.bottom:t.top)+n*i):"line"==r&&(o=0<n?t.bottom+3:t.top-3);(l=Bn(e,a,o)).outside;){if(n<0?o<=0:o>=s.height){l.hitSide=!0;break}o+=5*n}return l}function Ll(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new E,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null}function kl(e,t){var n=Cn(e,t.line);if(!n||n.hidden)return null;var r=Xe(e.doc,t.line),n=wn(n,r,t.line),r=fe(r,e.doc.direction),e="left";r&&(e=oe(r,t.ch)%2?"right":"left");e=Mn(n.map,t.ch,e);return e.offset="right"==e.collapse?e.end:e.start,e}function Tl(e,t){return t&&(e.bad=!0),e}function Ml(e,t,n){var r;if(t==e.display.lineDiv){if(!(r=e.display.lineDiv.childNodes[n]))return Tl(e.clipPos(et(e.display.viewTo-1)),!0);t=null,n=0}else for(r=t;;r=r.parentNode){if(!r||r==e.display.lineDiv)return null;if(r.parentNode&&r.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==r)return function(u,e,t){var n=u.text.firstChild,r=!1;if(!e||!T(n,e))return Tl(et(qe(u.line),0),!0);if(e==n&&(r=!0,e=n.childNodes[t],t=0,!e)){var i=u.rest?X(u.rest):u.line;return Tl(et(qe(i),i.text.length),r)}var i=3==e.nodeType?e:null,o=e;i||1!=e.childNodes.length||3!=e.firstChild.nodeType||(i=e.firstChild,t=t&&i.nodeValue.length);for(;o.parentNode!=n;)o=o.parentNode;var c=u.measure,h=c.maps;function l(e,t,n){for(var r=-1;r<(h?h.length:0);r++)for(var i=r<0?c.map:h[r],o=0;o<i.length;o+=3){var l=i[o+2];if(l==e||l==t){var s=qe(r<0?u.line:u.rest[r]),a=i[o]+n;return et(s,a=n<0||l!=e?i[o+(n?1:0)]:a)}}}var s=l(i,o,t);if(s)return Tl(s,r);for(var a=o.nextSibling,d=i?i.nodeValue.length-t:0;a;a=a.nextSibling){if(s=l(a,a.firstChild,0))return Tl(et(s.line,s.ch-d),r);d+=a.textContent.length}for(var f=o.previousSibling,p=t;f;f=f.previousSibling){if(s=l(f,f.firstChild,-1))return Tl(et(s.line,s.ch+p),r);p+=f.textContent.length}}(o,t,n)}}Ll.prototype.init=function(e){var t=this,o=this,l=o.cm,s=o.div=e.lineDiv;function a(e){for(var t=e.target;t;t=t.parentNode){if(t==s)return 1;if(/\bCodeMirror-(?:line)?widget\b/.test(t.className))break}}function n(e){if(a(e)&&!be(l,e)){if(l.somethingSelected())gl({lineWise:!1,text:l.getSelections()}),"cut"==e.type&&l.replaceSelection("",null,"cut");else{if(!l.options.lineWiseCopyCut)return;var t=bl(l);gl({lineWise:!0,text:t.text}),"cut"==e.type&&l.operation(function(){l.setSelections(t.ranges,0,B),l.replaceSelection("",null,"cut")})}if(e.clipboardData){e.clipboardData.clearData();var n=pl.text.join("\n");if(e.clipboardData.setData("Text",n),e.clipboardData.getData("Text")==n)return void e.preventDefault()}var r=xl(),e=r.firstChild;l.display.lineSpace.insertBefore(r,l.display.lineSpace.firstChild),e.value=pl.text.join("\n");var i=N();W(e),setTimeout(function(){l.display.lineSpace.removeChild(r),i.focus(),i==s&&o.showPrimarySelection()},50)}}s.contentEditable=!0,wl(s,l.options.spellcheck,l.options.autocorrect,l.options.autocapitalize),ge(s,"paste",function(e){!a(e)||be(l,e)||vl(e,l)||v<=11&&setTimeout(Ir(l,function(){return t.updateFromDOM()}),20)}),ge(s,"compositionstart",function(e){t.composing={data:e.data,done:!1}}),ge(s,"compositionupdate",function(e){t.composing||(t.composing={data:e.data,done:!1})}),ge(s,"compositionend",function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)}),ge(s,"touchstart",function(){return o.forceCompositionEnd()}),ge(s,"input",function(){t.composing||t.readFromDOMSoon()}),ge(s,"copy",n),ge(s,"cut",n)},Ll.prototype.screenReaderLabelChanged=function(e){e?this.div.setAttribute("aria-label",e):this.div.removeAttribute("aria-label")},Ll.prototype.prepareSelection=function(){var e=lr(this.cm,!1);return e.focus=N()==this.div,e},Ll.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},Ll.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},Ll.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,n=t.doc.sel.primary(),r=n.from(),i=n.to();if(t.display.viewTo==t.display.viewFrom||r.line>=t.display.viewTo||i.line<t.display.viewFrom)e.removeAllRanges();else{var o=Ml(t,e.anchorNode,e.anchorOffset),n=Ml(t,e.focusNode,e.focusOffset);if(!o||o.bad||!n||n.bad||0!=tt(ot(o,n),r)||0!=tt(it(o,n),i)){var n=t.display.view,r=r.line>=t.display.viewFrom&&kl(t,r)||{node:n[0].measure.map[2],offset:0},i=i.line<t.display.viewTo&&kl(t,i);if(i||(i={node:(s=(s=n[n.length-1].measure).maps?s.maps[s.maps.length-1]:s.map)[s.length-1],offset:s[s.length-2]-s[s.length-3]}),r&&i){var l,s=e.rangeCount&&e.getRangeAt(0);try{l=D(r.node,r.offset,i.offset,i.node)}catch(e){}l&&(!d&&t.state.focused?(e.collapse(r.node,r.offset),l.collapsed||(e.removeAllRanges(),e.addRange(l))):(e.removeAllRanges(),e.addRange(l)),s&&null==e.anchorNode?e.addRange(s):d&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},Ll.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation(function(){return e.cm.curOp.selectionChanged=!0})},20)},Ll.prototype.showMultipleSelections=function(e){L(this.cm.display.cursorDiv,e.cursors),L(this.cm.display.selectionDiv,e.selection)},Ll.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},Ll.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;e=e.getRangeAt(0).commonAncestorContainer;return T(this.div,e)},Ll.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()&&N()==this.div||this.showSelection(this.prepareSelection(),!0),this.div.focus())},Ll.prototype.blur=function(){this.div.blur()},Ll.prototype.getField=function(){return this.div},Ll.prototype.supportsTouch=function(){return!0},Ll.prototype.receivedFocus=function(){var e=this,t=this;this.selectionInEditor()?setTimeout(function(){return e.pollSelection()},20):Er(this.cm,function(){return t.cm.curOp.selectionChanged=!0}),this.polling.set(this.cm.options.pollInterval,function e(){t.cm.state.focused&&(t.pollSelection(),t.polling.set(t.cm.options.pollInterval,e))})},Ll.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},Ll.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e,t,n=this.getSelection(),r=this.cm;if(a&&o&&this.cm.display.gutterSpecs.length&&function(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}(n.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();this.composing||(this.rememberSelection(),e=Ml(r,n.anchorNode,n.anchorOffset),t=Ml(r,n.focusNode,n.focusOffset),e&&t&&Er(r,function(){Ei(r.doc,ai(e,t),B),(e.bad||t.bad)&&(r.curOp.selectionChanged=!0)}))}},Ll.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t=this.cm,n=t.display,r=t.doc.sel.primary(),i=r.from(),r=r.to();if(0==i.ch&&i.line>t.firstLine()&&(i=et(i.line-1,Xe(t.doc,i.line-1).length)),r.ch==Xe(t.doc,r.line).text.length&&r.line<t.lastLine()&&(r=et(r.line+1,0)),i.line<n.viewFrom||r.line>n.viewTo-1)return!1;var o,l=i.line==n.viewFrom||0==(l=Jn(t,i.line))?(e=qe(n.view[0].line),n.view[0].node):(e=qe(n.view[l].line),n.view[l-1].node.nextSibling),r=Jn(t,r.line),r=r==n.view.length-1?(o=n.viewTo-1,n.lineDiv.lastChild):(o=qe(n.view[r+1].line)-1,n.view[r+1].node.previousSibling);if(!l)return!1;for(var s=t.doc.splitLines(function(l,e,t,s,a){var n="",u=!1,c=l.doc.lineSeparator(),h=!1;function d(){u&&(n+=c,h&&(n+=c),u=h=!1)}function f(e){e&&(d(),n+=e)}for(;!function e(t){if(1==t.nodeType){var n=t.getAttribute("cm-text");if(n)f(n);else if(n=t.getAttribute("cm-marker"))(n=l.findMarks(et(s,0),et(a+1,0),(o=+n,function(e){return e.id==o}))).length&&(r=n[0].find(0))&&f(Ye(l.doc,r.from,r.to).join(c));else if("false"!=t.getAttribute("contenteditable")){var r=/^(pre|div|p|li|table|br)$/i.test(t.nodeName);if(/^br$/i.test(t.nodeName)||0!=t.textContent.length){r&&d();for(var i=0;i<t.childNodes.length;i++)e(t.childNodes[i]);/^(pre|p)$/i.test(t.nodeName)&&(h=!0),r&&(u=!0)}}}else 3==t.nodeType&&f(t.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "));var o}(e),e!=t;)e=e.nextSibling,h=!1;return n}(t,l,r,e,o)),a=Ye(t.doc,et(e,0),et(o,Xe(t.doc,o).text.length));1<s.length&&1<a.length;)if(X(s)==X(a))s.pop(),a.pop(),o--;else{if(s[0]!=a[0])break;s.shift(),a.shift(),e++}for(var u=0,c=0,h=s[0],d=a[0],f=Math.min(h.length,d.length);u<f&&h.charCodeAt(u)==d.charCodeAt(u);)++u;for(var p=X(s),g=X(a),m=Math.min(p.length-(1==s.length?u:0),g.length-(1==a.length?u:0));c<m&&p.charCodeAt(p.length-c-1)==g.charCodeAt(g.length-c-1);)++c;if(1==s.length&&1==a.length&&e==i.line)for(;u&&u>i.ch&&p.charCodeAt(p.length-c-1)==g.charCodeAt(g.length-c-1);)u--,c++;s[s.length-1]=p.slice(0,p.length-c).replace(/^\u200b+/,""),s[0]=s[0].slice(u).replace(/\u200b+$/,"");l=et(e,u),r=et(o,a.length?X(a).length-c:0);return 1<s.length||s[0]||tt(l,r)?(Zi(t.doc,s,l,r,"+input"),!0):void 0},Ll.prototype.ensurePolled=function(){this.forceCompositionEnd()},Ll.prototype.reset=function(){this.forceCompositionEnd()},Ll.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},Ll.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout(function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()},80))},Ll.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||Er(this.cm,function(){return er(e.cm)})},Ll.prototype.setUneditable=function(e){e.contentEditable="false"},Ll.prototype.onKeyPress=function(e){0==e.charCode||this.composing||(e.preventDefault(),this.cm.isReadOnly()||Ir(this.cm,ml)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},Ll.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},Ll.prototype.onContextMenu=function(){},Ll.prototype.resetPosition=function(){},Ll.prototype.needsContentAttribute=!0;function Nl(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new E,this.hasSelection=!1,this.composing=null}var Ol,Al,Dl,Wl,Hl;function Fl(e,t,r,n){Ol.defaults[e]=t,r&&(Al[e]=n?function(e,t,n){n!=ll&&r(e,t,n)}:r)}Nl.prototype.init=function(n){var e=this,r=this,i=this.cm;this.createField(n);var o=this.textarea;function t(e){if(!be(i,e)){if(i.somethingSelected())gl({lineWise:!1,text:i.getSelections()});else{if(!i.options.lineWiseCopyCut)return;var t=bl(i);gl({lineWise:!0,text:t.text}),"cut"==e.type?i.setSelections(t.ranges,null,B):(r.prevInput="",o.value=t.text.join("\n"),W(o))}"cut"==e.type&&(i.state.cutIncoming=+new Date)}}n.wrapper.insertBefore(this.wrapper,n.wrapper.firstChild),s&&(o.style.width="0px"),ge(o,"input",function(){w&&9<=v&&e.hasSelection&&(e.hasSelection=null),r.poll()}),ge(o,"paste",function(e){be(i,e)||vl(e,i)||(i.state.pasteIncoming=+new Date,r.fastPoll())}),ge(o,"cut",t),ge(o,"copy",t),ge(n.scroller,"paste",function(e){if(!fn(n,e)&&!be(i,e)){if(!o.dispatchEvent)return i.state.pasteIncoming=+new Date,void r.focus();var t=new Event("paste");t.clipboardData=e.clipboardData,o.dispatchEvent(t)}}),ge(n.lineSpace,"selectstart",function(e){fn(n,e)||Se(e)}),ge(o,"compositionstart",function(){var e=i.getCursor("from");r.composing&&r.composing.range.clear(),r.composing={start:e,range:i.markText(e,i.getCursor("to"),{className:"CodeMirror-composing"})}}),ge(o,"compositionend",function(){r.composing&&(r.poll(),r.composing.range.clear(),r.composing=null)})},Nl.prototype.createField=function(e){this.wrapper=xl(),this.textarea=this.wrapper.firstChild},Nl.prototype.screenReaderLabelChanged=function(e){e?this.textarea.setAttribute("aria-label",e):this.textarea.removeAttribute("aria-label")},Nl.prototype.prepareSelection=function(){var e,t=this.cm,n=t.display,r=t.doc,i=lr(t);return t.options.moveInputWithCursor&&(e=In(t,r.sel.primary().head,"div"),t=n.wrapper.getBoundingClientRect(),r=n.lineDiv.getBoundingClientRect(),i.teTop=Math.max(0,Math.min(n.wrapper.clientHeight-10,e.top+r.top-t.top)),i.teLeft=Math.max(0,Math.min(n.wrapper.clientWidth-10,e.left+r.left-t.left))),i},Nl.prototype.showSelection=function(e){var t=this.cm.display;L(t.cursorDiv,e.cursors),L(t.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},Nl.prototype.reset=function(e){var t,n;this.contextMenuPending||this.composing||((t=this.cm).somethingSelected()?(this.prevInput="",n=t.getSelection(),this.textarea.value=n,t.state.focused&&W(this.textarea),w&&9<=v&&(this.hasSelection=n)):e||(this.prevInput=this.textarea.value="",w&&9<=v&&(this.hasSelection=null)))},Nl.prototype.getField=function(){return this.textarea},Nl.prototype.supportsTouch=function(){return!1},Nl.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!h||N()!=this.textarea))try{this.textarea.focus()}catch(e){}},Nl.prototype.blur=function(){this.textarea.blur()},Nl.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},Nl.prototype.receivedFocus=function(){this.slowPoll()},Nl.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,function(){e.poll(),e.cm.state.focused&&e.slowPoll()})},Nl.prototype.fastPoll=function(){var t=!1,n=this;n.pollingFast=!0,n.polling.set(20,function e(){n.poll()||t?(n.pollingFast=!1,n.slowPoll()):(t=!0,n.polling.set(60,e))})},Nl.prototype.poll=function(){var e=this,t=this.cm,n=this.textarea,r=this.prevInput;if(this.contextMenuPending||!t.state.focused||He(n)&&!r&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=n.value;if(i==r&&!t.somethingSelected())return!1;if(w&&9<=v&&this.hasSelection===i||g&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||r||(r="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var l=0,s=Math.min(r.length,i.length);l<s&&r.charCodeAt(l)==i.charCodeAt(l);)++l;return Er(t,function(){ml(t,i.slice(l),r.length-l,null,e.composing?"*compose":null),1e3<i.length||-1<i.indexOf("\n")?n.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},Nl.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},Nl.prototype.onKeyPress=function(){w&&9<=v&&(this.hasSelection=null),this.fastPoll()},Nl.prototype.onContextMenu=function(e){var n=this,r=n.cm,i=r.display,o=n.textarea;n.contextMenuPending&&n.contextMenuPending();var l,s,t,a,u=Qn(r,e),c=i.scroller.scrollTop;function h(){var e,t;null!=o.selectionStart&&(t="​"+((e=r.somethingSelected())?o.value:""),o.value="⇚",o.value=t,n.prevInput=e?"":"​",o.selectionStart=1,o.selectionEnd=t.length,i.selForContextMenu=r.doc.sel)}function d(){var e,t;n.contextMenuPending==d&&(n.contextMenuPending=!1,n.wrapper.style.cssText=s,o.style.cssText=l,w&&v<9&&i.scrollbars.setScrollTop(i.scroller.scrollTop=c),null!=o.selectionStart&&((!w||v<9)&&h(),e=0,t=function(){i.selForContextMenu==r.doc.sel&&0==o.selectionStart&&0<o.selectionEnd&&"​"==n.prevInput?Ir(r,Ki)(r):e++<10?i.detectingSelectAll=setTimeout(t,500):(i.selForContextMenu=null,i.input.reset())},i.detectingSelectAll=setTimeout(t,200)))}u&&!p&&(r.options.resetSelectionOnContextMenu&&-1==r.doc.sel.contains(u)&&Ir(r,Ei)(r.doc,ai(u),B),l=o.style.cssText,s=n.wrapper.style.cssText,u=n.wrapper.offsetParent.getBoundingClientRect(),n.wrapper.style.cssText="position: static",o.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-u.top-5)+"px; left: "+(e.clientX-u.left-5)+"px;\n      z-index: 1000; background: "+(w?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",f&&(t=window.scrollY),i.input.focus(),f&&window.scrollTo(null,t),i.input.reset(),r.somethingSelected()||(o.value=n.prevInput=" "),n.contextMenuPending=d,i.selForContextMenu=r.doc.sel,clearTimeout(i.detectingSelectAll),w&&9<=v&&h(),b?(Te(e),a=function(){ve(window,"mouseup",a),setTimeout(d,20)},ge(window,"mouseup",a)):setTimeout(d,50))},Nl.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e,this.textarea.readOnly=!!e},Nl.prototype.setUneditable=function(){},Nl.prototype.needsContentAttribute=!1,Al=(Ol=hl).optionHandlers,Ol.defineOption=Fl,Ol.Init=ll,Fl("value","",function(e,t){return e.setValue(t)},!0),Fl("mode",null,function(e,t){e.doc.modeOption=t,fi(e)},!0),Fl("indentUnit",2,fi,!0),Fl("indentWithTabs",!1),Fl("smartIndent",!0),Fl("tabSize",4,function(e){pi(e),An(e),er(e)},!0),Fl("lineSeparator",null,function(e,r){if(e.doc.lineSep=r){var i=[],o=e.doc.first;e.doc.iter(function(e){for(var t=0;;){var n=e.text.indexOf(r,t);if(-1==n)break;t=n+r.length,i.push(et(o,n))}o++});for(var t=i.length-1;0<=t;t--)Zi(e.doc,r,i[t],et(i[t].line,i[t].ch+r.length))}}),Fl("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b\u200e\u200f\u2028\u2029\ufeff\ufff9-\ufffc]/g,function(e,t,n){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),n!=ll&&e.refresh()}),Fl("specialCharPlaceholder",$t,function(e){return e.refresh()},!0),Fl("electricChars",!0),Fl("inputStyle",h?"contenteditable":"textarea",function(){throw new Error("inputStyle can not (yet) be changed in a running editor")},!0),Fl("spellcheck",!1,function(e,t){return e.getInputField().spellcheck=t},!0),Fl("autocorrect",!1,function(e,t){return e.getInputField().autocorrect=t},!0),Fl("autocapitalize",!1,function(e,t){return e.getInputField().autocapitalize=t},!0),Fl("rtlMoveVisually",!t),Fl("wholeLineUpdateBefore",!0),Fl("theme","default",function(e){ol(e),Qr(e)},!0),Fl("keyMap","default",function(e,t,n){t=Ho(t),n=n!=ll&&Ho(n);n&&n.detach&&n.detach(e,t),t.attach&&t.attach(e,n||null)}),Fl("extraKeys",null),Fl("configureMouse",null),Fl("lineWrapping",!1,cl,!0),Fl("gutters",[],function(e,t){e.display.gutterSpecs=qr(t,e.options.lineNumbers),Qr(e)},!0),Fl("fixedGutter",!0,function(e,t){e.display.gutters.style.left=t?_n(e.display)+"px":"0",e.refresh()},!0),Fl("coverGutterNextToScrollbar",!1,function(e){return Or(e)},!0),Fl("scrollbarStyle","native",function(e){Wr(e),Or(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)},!0),Fl("lineNumbers",!1,function(e,t){e.display.gutterSpecs=qr(e.options.gutters,t),Qr(e)},!0),Fl("firstLineNumber",1,Qr,!0),Fl("lineNumberFormatter",function(e){return e},Qr,!0),Fl("showCursorWhenSelecting",!1,or,!0),Fl("resetSelectionOnContextMenu",!0),Fl("lineWiseCopyCut",!0),Fl("pasteLinesPerSelection",!0),Fl("selectionsMayTouch",!1),Fl("readOnly",!1,function(e,t){"nocursor"==t&&(fr(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)}),Fl("screenReaderLabel",null,function(e,t){e.display.input.screenReaderLabelChanged(t=""===t?null:t)}),Fl("disableInput",!1,function(e,t){t||e.display.input.reset()},!0),Fl("dragDrop",!0,ul),Fl("allowDropFileTypes",null),Fl("cursorBlinkRate",530),Fl("cursorScrollMargin",0),Fl("cursorHeight",1,or,!0),Fl("singleCursorHeightPerLine",!0,or,!0),Fl("workTime",100),Fl("workDelay",100),Fl("flattenSpans",!0,pi,!0),Fl("addModeClass",!1,pi,!0),Fl("pollInterval",100),Fl("undoDepth",200,function(e,t){return e.doc.history.undoDepth=t}),Fl("historyEventDelay",1250),Fl("viewportMargin",10,function(e){return e.refresh()},!0),Fl("maxHighlightLength",1e4,pi,!0),Fl("moveInputWithCursor",!0,function(e,t){t||e.display.input.resetPosition()}),Fl("tabindex",null,function(e,t){return e.display.input.getField().tabIndex=t||""}),Fl("autofocus",null),Fl("direction","ltr",function(e,t){return e.doc.setDirection(t)},!0),Fl("phrases",null),Wl=(Dl=hl).optionHandlers,Hl=Dl.helpers={},Dl.prototype={constructor:Dl,focus:function(){window.focus(),this.display.input.focus()},setOption:function(e,t){var n=this.options,r=n[e];n[e]==t&&"mode"!=e||(n[e]=t,Wl.hasOwnProperty(e)&&Ir(this,Wl[e])(this,t,r),ye(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](Ho(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,n=0;n<t.length;++n)if(t[n]==e||t[n].name==e)return t.splice(n,1),!0},addOverlay:Rr(function(e,t){var n=e.token?e:Dl.getMode(this.options,e);if(n.startState)throw new Error("Overlays may not be stateful.");!function(e,t,n){for(var r=0,i=n(t);r<e.length&&n(e[r])<=i;)r++;e.splice(r,0,t)}(this.state.overlays,{mode:n,modeSpec:e,opaque:t&&t.opaque,priority:t&&t.priority||0},function(e){return e.priority}),this.state.modeGen++,er(this)}),removeOverlay:Rr(function(e){for(var t=this.state.overlays,n=0;n<t.length;++n){var r=t[n].modeSpec;if(r==e||"string"==typeof e&&r.name==e)return t.splice(n,1),this.state.modeGen++,void er(this)}}),indentLine:Rr(function(e,t,n){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),Qe(this.doc,e)&&fl(this,e,t,n)}),indentSelection:Rr(function(e){for(var t=this.doc.sel.ranges,n=-1,r=0;r<t.length;r++){var i=t[r];if(i.empty())i.head.line>n&&(fl(this,i.head.line,e,!0),n=i.head.line,r==this.doc.sel.primIndex&&br(this));else{for(var o=i.from(),l=i.to(),i=Math.max(n,o.line),n=Math.min(this.lastLine(),l.line-(l.ch?0:1))+1,s=i;s<n;++s)fl(this,s,e);i=this.doc.sel.ranges;0==o.ch&&t.length==i.length&&0<i[r].from().ch&&Hi(this.doc,r,new li(o,i[r].to()),B)}}}),getTokenAt:function(e,t){return yt(this,e,t)},getLineTokens:function(e,t){return yt(this,et(e),t,!0)},getTokenTypeAt:function(e){e=st(this.doc,e);var t,n=dt(this,Xe(this.doc,e.line)),r=0,i=(n.length-1)/2,o=e.ch;if(0==o)t=n[2];else for(;;){var l=r+i>>1;if((l?n[2*l-1]:0)>=o)i=l;else{if(!(n[2*l+1]<o)){t=n[2*l+2];break}r=1+l}}e=t?t.indexOf("overlay "):-1;return e<0?t:0==e?null:t.slice(0,e-1)},getModeAt:function(e){var t=this.doc.mode;return t.innerMode?Dl.innerMode(t,this.getTokenAt(e).state).mode:t},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var n=[];if(!Hl.hasOwnProperty(t))return n;var r=Hl[t],i=this.getModeAt(e);if("string"==typeof i[t])r[i[t]]&&n.push(r[i[t]]);else if(i[t])for(var o=0;o<i[t].length;o++){var l=r[i[t][o]];l&&n.push(l)}else i.helperType&&r[i.helperType]?n.push(r[i.helperType]):r[i.name]&&n.push(r[i.name]);for(var s=0;s<r._global.length;s++){var a=r._global[s];a.pred(i,this)&&-1==I(n,a.val)&&n.push(a.val)}return n},getStateAfter:function(e,t){var n=this.doc;return ft(this,(e=lt(n,null==e?n.first+n.size-1:e))+1,t).state},cursorCoords:function(e,t){var n=this.doc.sel.primary(),n=null==e?n.head:"object"==typeof e?st(this.doc,e):e?n.from():n.to();return In(this,n,t||"page")},charCoords:function(e,t){return En(this,st(this.doc,e),t||"page")},coordsChar:function(e,t){return Bn(this,(e=Pn(this,e,t||"page")).left,e.top)},lineAtHeight:function(e,t){return e=Pn(this,{top:e,left:0},t||"page").top,Ze(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,n){var r,i=!1,e="number"==typeof e?(r=this.doc.first+this.doc.size-1,e<this.doc.first?e=this.doc.first:r<e&&(e=r,i=!0),Xe(this.doc,e)):e;return Fn(this,e,{top:0,left:0},t||"page",n||i).top+(i?this.doc.height-Bt(e):0)},defaultTextHeight:function(){return Xn(this.display)},defaultCharWidth:function(){return Yn(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,n,r,i){var o,l,s=this.display,a=(e=In(this,st(this.doc,e))).bottom,u=e.left;t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),s.sizer.appendChild(t),"over"==r?a=e.top:"above"!=r&&"near"!=r||(o=Math.max(s.wrapper.clientHeight,this.doc.height),l=Math.max(s.sizer.clientWidth,s.lineSpace.clientWidth),("above"==r||e.bottom+t.offsetHeight>o)&&e.top>t.offsetHeight?a=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=o&&(a=e.bottom),u+t.offsetWidth>l&&(u=l-t.offsetWidth)),t.style.top=a+"px",t.style.left=t.style.right="","right"==i?(u=s.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?u=0:"middle"==i&&(u=(s.sizer.clientWidth-t.offsetWidth)/2),t.style.left=u+"px"),n&&(n=this,t={left:u,top:a,right:u+t.offsetWidth,bottom:a+t.offsetHeight},null!=(t=vr(n,t)).scrollTop&&Sr(n,t.scrollTop),null!=t.scrollLeft&&kr(n,t.scrollLeft))},triggerOnKeyDown:Rr($o),triggerOnKeyPress:Rr(qo),triggerOnKeyUp:_o,triggerOnMouseDown:Rr(el),execCommand:function(e){if(zo.hasOwnProperty(e))return zo[e].call(null,this)},triggerElectric:Rr(function(e){yl(this,e)}),findPosH:function(e,t,n,r){var i=1;t<0&&(i=-1,t=-t);for(var o=st(this.doc,e),l=0;l<t&&!(o=Cl(this.doc,o,i,n,r)).hitSide;++l);return o},moveH:Rr(function(t,n){var r=this;this.extendSelectionsBy(function(e){return r.display.shift||r.doc.extend||e.empty()?Cl(r.doc,e.head,t,n,r.options.rtlMoveVisually):t<0?e.from():e.to()},U)}),deleteH:Rr(function(n,r){var e=this.doc.sel,i=this.doc;e.somethingSelected()?i.replaceSelection("",null,"+delete"):Fo(this,function(e){var t=Cl(i,e.head,n,r,!1);return n<0?{from:t,to:e.head}:{from:e.head,to:t}})}),findPosV:function(e,t,n,r){var i=1,o=r;t<0&&(i=-1,t=-t);for(var l=st(this.doc,e),s=0;s<t;++s){var a=In(this,l,"div");if(null==o?o=a.left:a.left=o,(l=Sl(this,a,i,n)).hitSide)break}return l},moveV:Rr(function(r,i){var o=this,l=this.doc,s=[],a=!this.display.shift&&!l.extend&&l.sel.somethingSelected();if(l.extendSelectionsBy(function(e){if(a)return r<0?e.from():e.to();var t=In(o,e.head,"div");null!=e.goalColumn&&(t.left=e.goalColumn),s.push(t.left);var n=Sl(o,t,r,i);return"page"==i&&e==l.sel.primary()&&yr(o,En(o,n,"div").top-t.top),n},U),s.length)for(var e=0;e<l.sel.ranges.length;e++)l.sel.ranges[e].goalColumn=s[e]}),findWordAt:function(e){var t=Xe(this.doc,e.line).text,n=e.ch,r=e.ch;if(t){var i=this.getHelper(e,"wordChars");"before"!=e.sticky&&r!=t.length||!n?++r:--n;for(var o=t.charAt(n),l=Q(o,i)?function(e){return Q(e,i)}:/\s/.test(o)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!Q(e)};0<n&&l(t.charAt(n-1));)--n;for(;r<t.length&&l(t.charAt(r));)++r}return new li(et(e.line,n),et(e.line,r))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||(((this.state.overwrite=!this.state.overwrite)?O:C)(this.display.cursorDiv,"CodeMirror-overwrite"),ye(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==N()},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:Rr(function(e,t){wr(this,e,t)}),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-vn(this)-this.display.barHeight,width:e.scrollWidth-vn(this)-this.display.barWidth,clientHeight:bn(this),clientWidth:yn(this)}},scrollIntoView:Rr(function(e,t){var n;null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:et(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?(n=e,xr(t=this),t.curOp.scrollToPos=n):Cr(this,e.from,e.to,e.margin)}),setSize:Rr(function(e,t){function n(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e}var r=this;null!=e&&(this.display.wrapper.style.width=n(e)),null!=t&&(this.display.wrapper.style.height=n(t)),this.options.lineWrapping&&On(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){tr(r,i,"widget");break}++i}),this.curOp.forceUpdate=!0,ye(this,"refresh",this)}),operation:function(e){return Er(this,e)},startOperation:function(){return Fr(this)},endOperation:function(){return Pr(this)},refresh:Rr(function(){var e=this.display.cachedTextHeight;er(this),this.curOp.forceUpdate=!0,An(this),wr(this,this.doc.scrollLeft,this.doc.scrollTop),Xr(this.display),(null==e||.5<Math.abs(e-Xn(this.display))||this.options.lineWrapping)&&Zn(this),ye(this,"refresh",this)}),swapDoc:Rr(function(e){var t=this.doc;return t.cm=null,this.state.selectingText&&this.state.selectingText(),yi(this,e),An(this),this.display.input.reset(),wr(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,nn(this,"swapDoc",this,t),t}),phrase:function(e){var t=this.options.phrases;return t&&Object.prototype.hasOwnProperty.call(t,e)?t[e]:e},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},Ce(Dl),Dl.registerHelper=function(e,t,n){Hl.hasOwnProperty(e)||(Hl[e]=Dl[e]={_global:[]}),Hl[e][t]=n},Dl.registerGlobalHelper=function(e,t,n,r){Dl.registerHelper(e,t,r),Hl[e]._global.push({pred:n,val:r})};var Pl,El="iter insert remove copy getEditor constructor".split(" ");for(Pl in fo.prototype)fo.prototype.hasOwnProperty(Pl)&&I(El,Pl)<0&&(hl.prototype[Pl]=function(e){return function(){return e.apply(this.doc,arguments)}}(fo.prototype[Pl]));return Ce(fo),hl.inputStyles={textarea:Nl,contenteditable:Ll},hl.defineMode=function(e){hl.defaults.mode||"null"==e||(hl.defaults.mode=e),function(e,t){2<arguments.length&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Ee[e]=t}.apply(this,arguments)},hl.defineMIME=function(e,t){Ie[e]=t},hl.defineMode("null",function(){return{token:function(e){return e.skipToEnd()}}}),hl.defineMIME("text/plain","null"),hl.defineExtension=function(e,t){hl.prototype[e]=t},hl.defineDocExtension=function(e,t){fo.prototype[e]=t},hl.fromTextArea=function(t,n){var e;function r(){t.value=s.getValue()}if((n=n?F(n):{}).value=t.value,!n.tabindex&&t.tabIndex&&(n.tabindex=t.tabIndex),!n.placeholder&&t.placeholder&&(n.placeholder=t.placeholder),null==n.autofocus&&(e=N(),n.autofocus=e==t||null!=t.getAttribute("autofocus")&&e==document.body),t.form&&(ge(t.form,"submit",r),!n.leaveSubmitMethodAlone)){var i=t.form,o=i.submit;try{var l=i.submit=function(){r(),i.submit=o,i.submit(),i.submit=l}}catch(e){}}n.finishInit=function(e){e.save=r,e.getTextArea=function(){return t},e.toTextArea=function(){e.toTextArea=isNaN,r(),t.parentNode.removeChild(e.getWrapperElement()),t.style.display="",t.form&&(ve(t.form,"submit",r),n.leaveSubmitMethodAlone||"function"!=typeof t.form.submit||(t.form.submit=o))}},t.style.display="none";var s=hl(function(e){return t.parentNode.insertBefore(e,t.nextSibling)},n);return s},(t=hl).off=ve,t.on=ge,t.wheelEventPixels=ri,t.Doc=fo,t.splitLines=We,t.countColumn=P,t.findColumn=V,t.isWordChar=Z,t.Pass=z,t.signal=ye,t.Line=Vt,t.changeEnd=ui,t.scrollbarModel=Dr,t.Pos=et,t.cmpPos=tt,t.modes=Ee,t.mimeModes=Ie,t.resolveMode=Re,t.getMode=ze,t.modeExtensions=Be,t.extendMode=Ge,t.copyState=Ue,t.startState=Ke,t.innerMode=Ve,t.commands=zo,t.keyMap=To,t.keyName=Wo,t.isModifierKey=Ao,t.lookupKey=Oo,t.normalizeKeyMap=No,t.StringStream=je,t.SharedTextMarker=uo,t.TextMarker=lo,t.LineWidget=io,t.e_preventDefault=Se,t.e_stopPropagation=Le,t.e_stop=Te,t.addClass=O,t.contains=T,t.rmClass=C,t.keyNames=Co,hl.version="5.65.2",hl}),function(t){"function"==typeof t.define&&t.define("core",["codemirror.js"],function(e){t.CodeMirror=e})}(this);